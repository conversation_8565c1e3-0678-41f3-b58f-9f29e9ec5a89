#!/usr/bin/env python3
"""
Direct API test for rate limiting verification.
This bypasses the WebSocket and tests the core rate limiting functionality.
"""

import asyncio
import time
import logging
from multi_tool_agent.utils.rate_limiter import get_hybrid_manager, KimiRateLimiter, RateLimitConfig
from multi_tool_agent.config import AgentConfig

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_rate_limiter_directly():
    """Test the rate limiter directly without going through the server."""
    logger.info("🧪 Testing rate limiter directly...")
    
    # Create a rate limiter with strict settings
    config = RateLimitConfig(
        rpm=10,
        min_interval=6.5,
        max_queue_size=50,
        queue_timeout=30.0,
        enable_fallback=True
    )
    
    limiter = KimiRateLimiter(config)
    
    # Try to make 15 rapid requests
    results = []
    start_time = time.time()
    
    for i in range(15):
        try:
            # Check if rate limited
            is_limited = await limiter.is_rate_limited()
            
            if not is_limited:
                # Simulate making a request
                await limiter.record_request()
                results.append({
                    'index': i,
                    'success': True,
                    'timestamp': time.time(),
                    'rate_limited': False
                })
                logger.info(f"✅ Request {i} allowed")
            else:
                results.append({
                    'index': i,
                    'success': False,
                    'timestamp': time.time(),
                    'rate_limited': True
                })
                logger.warning(f"🚫 Request {i} rate limited")
                
                # Wait for availability
                if await limiter.wait_for_availability():
                    await limiter.record_request()
                    logger.info(f"✅ Request {i} allowed after waiting")
                    
        except Exception as e:
            logger.error(f"❌ Request {i} failed: {e}")
            results.append({
                'index': i,
                'success': False,
                'timestamp': time.time(),
                'error': str(e)
            })
    
    # Analyze results
    total_time = time.time() - start_time
    successful = [r for r in results if r.get('success', False)]
    rate_limited = [r for r in results if r.get('rate_limited', False)]
    
    logger.info(f"\n📊 Direct Rate Limiter Test Results")
    logger.info("=" * 50)
    logger.info(f"Total time: {total_time:.2f}s")
    logger.info(f"Total requests: {len(results)}")
    logger.info(f"Successful: {len(successful)}")
    logger.info(f"Rate limited: {len(rate_limited)}")
    logger.info(f"Effective RPM: {(len(successful) / total_time) * 60:.1f}")
    
    # Check if rate limiting is working
    if (len(successful) / total_time) * 60 > 10:
        logger.error("❌ Rate limiting NOT working! Exceeded 10 RPM")
    else:
        logger.info("✅ Rate limiting is working correctly")

async def test_hybrid_manager():
    """Test the hybrid model manager."""
    logger.info("\n🧪 Testing hybrid model manager...")
    
    manager = get_hybrid_manager()
    
    # Test model selection for different tasks
    test_cases = [
        ("reasoning", True),      # Should use Kimi
        ("orchestration", True),  # Should use Kimi
        ("tool_execution", False), # Should use Gemini
        ("search", False),        # Should use Gemini
        ("formatting", False),    # Should use Gemini
    ]
    
    for task_type, is_critical in test_cases:
        model = manager.get_model_for_task(task_type, is_critical)
        should_use_kimi = await manager.should_use_kimi(task_type, is_critical)
        
        logger.info(f"Task: {task_type} (critical={is_critical})")
        logger.info(f"  Selected model: {model}")
        logger.info(f"  Should use Kimi: {should_use_kimi}")
        
        if "kimi" in model.lower() and task_type not in ["reasoning", "orchestration", "final_output", "tool_calling"]:
            logger.warning(f"  ⚠️ Using Kimi for {task_type} - should use Gemini!")

async def test_actual_api_calls():
    """Test with actual API call simulation."""
    logger.info("\n🧪 Testing with simulated API calls...")
    
    from multi_tool_agent.models.rate_limited_llm import RateLimitedLiteLlm
    
    # Create a rate-limited model
    model = RateLimitedLiteLlm(
        model=AgentConfig.KIMI_MODEL,
        task_type="reasoning",
        is_critical=True
    )
    
    # Try to make multiple calls
    results = []
    start_time = time.time()
    
    for i in range(12):
        try:
            request_start = time.time()
            
            # Check if we can use Kimi
            manager = model._hybrid_manager
            should_use_kimi = await manager.should_use_kimi("reasoning", True)
            
            if should_use_kimi:
                # Try to acquire rate limit slot
                try:
                    async with manager.kimi_limiter.acquire():
                        elapsed = time.time() - request_start
                        results.append({
                            'index': i,
                            'model': 'kimi',
                            'success': True,
                            'time': elapsed
                        })
                        logger.info(f"✅ Request {i} used Kimi (waited {elapsed:.2f}s)")
                except TimeoutError:
                    results.append({
                        'index': i,
                        'model': 'timeout',
                        'success': False
                    })
                    logger.warning(f"⏰ Request {i} timed out")
            else:
                # Use fallback
                elapsed = time.time() - request_start
                results.append({
                    'index': i,
                    'model': 'gemini',
                    'success': True,
                    'time': elapsed
                })
                logger.info(f"⚡ Request {i} used Gemini fallback")
                
            # Small delay between requests
            await asyncio.sleep(0.5)
            
        except Exception as e:
            logger.error(f"❌ Request {i} failed: {e}")
    
    # Analyze results
    total_time = time.time() - start_time
    kimi_calls = [r for r in results if r.get('model') == 'kimi']
    gemini_calls = [r for r in results if r.get('model') == 'gemini']
    
    logger.info(f"\n📊 API Call Simulation Results")
    logger.info("=" * 50)
    logger.info(f"Total time: {total_time:.2f}s")
    logger.info(f"Total requests: {len(results)}")
    logger.info(f"Kimi calls: {len(kimi_calls)}")
    logger.info(f"Gemini fallbacks: {len(gemini_calls)}")
    logger.info(f"Kimi RPM: {(len(kimi_calls) / total_time) * 60:.1f}")
    
    if (len(kimi_calls) / total_time) * 60 > 10:
        logger.error("❌ Kimi rate limit exceeded!")
    else:
        logger.info("✅ Kimi rate limit respected")

async def main():
    """Run all tests."""
    logger.info("🚀 Starting direct API rate limiting tests...")
    
    try:
        # Test 1: Direct rate limiter
        await test_rate_limiter_directly()
        
        # Test 2: Hybrid manager
        await test_hybrid_manager()
        
        # Test 3: API call simulation
        await test_actual_api_calls()
        
        logger.info("\n✅ All tests completed!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
