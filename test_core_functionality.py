"""
Standalone test for core back-to-back search functionality
Tests without ADK dependencies
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Mock the ToolContext since we're testing without ADK
class MockToolContext:
    pass

# Import the actual search functions
from multi_tool_agent.tools.activity_search_tools import _perform_advanced_search, _create_qdrant_filter, _find_day_planning_activities

async def test_qdrant_search():
    """Test direct Qdrant search functionality."""
    logger.info("=" * 60)
    logger.info("Testing Direct Qdrant Search")
    logger.info("=" * 60)
    
    try:
        # Create filters for 5-year-old in New Westminster
        filters = {
            "min_age": 5,
            "max_age": 5,
            "location": "New Westminster"
        }
        
        # Create Qdrant filter
        qdrant_filter = _create_qdrant_filter(filters)
        
        # Perform search
        logger.info("🔍 Searching for activities for 5-year-old in New Westminster...")
        results = await _perform_advanced_search(
            query="swimming gymnastics dance art sports music",
            qdrant_filter=qdrant_filter,
            limit=50
        )
        
        logger.info(f"✅ Found {len(results)} activities")
        
        # Group by facility
        facility_groups = {}
        for activity in results:
            facility = activity.get("facility", "Unknown")
            if facility not in facility_groups:
                facility_groups[facility] = []
            facility_groups[facility].append(activity)
        
        logger.info(f"🏢 Activities found across {len(facility_groups)} facilities:")
        for facility, activities in list(facility_groups.items())[:5]:
            logger.info(f"   - {facility}: {len(activities)} activities")
            
        return results
        
    except Exception as e:
        logger.error(f"❌ Search failed: {e}", exc_info=True)
        return []

async def test_day_planning():
    """Test the day planning functionality."""
    logger.info("\n" + "=" * 60)
    logger.info("Testing Day Planning (Back-to-Back)")
    logger.info("=" * 60)
    
    try:
        # First get some activities
        filters = {
            "min_age": 5,
            "max_age": 5,
            "location": "New Westminster"
        }
        
        qdrant_filter = _create_qdrant_filter(filters)
        activities = await _perform_advanced_search(
            query="activities",
            qdrant_filter=qdrant_filter,
            limit=100
        )
        
        if activities:
            logger.info(f"🔍 Analyzing {len(activities)} activities for back-to-back opportunities...")
            
            # Find day planning opportunities
            day_planning_pairs = _find_day_planning_activities(activities)
            
            logger.info(f"✅ Found {len(day_planning_pairs)} back-to-back opportunities")
            
            # Show some examples
            if day_planning_pairs:
                logger.info("\n📍 Sample back-to-back opportunities:")
                for i, pair in enumerate(day_planning_pairs[:3], 1):
                    logger.info(f"\n   Option {i}:")
                    logger.info(f"   - Date: {pair.get('date', 'Unknown')}")
                    logger.info(f"   - Gap: {pair.get('time_gap_minutes', 0)} minutes ({pair.get('gap_type', '')})")
                    logger.info(f"   - Cross-facility: {'Yes' if pair.get('cross_facility') else 'No'}")
                    
                    first = pair.get('first_class', {})
                    second = pair.get('second_class', {})
                    
                    logger.info(f"   - First: {first.get('name', 'Unknown')} at {first.get('facility', 'Unknown')}")
                    logger.info(f"     Time: {first.get('start_time_iso', '')} - {first.get('end_time_iso', '')}")
                    logger.info(f"   - Second: {second.get('name', 'Unknown')} at {second.get('facility', 'Unknown')}")
                    logger.info(f"     Time: {second.get('start_time_iso', '')} - {second.get('end_time_iso', '')}")
                    
            return day_planning_pairs
        else:
            logger.warning("⚠️ No activities found to analyze")
            return []
            
    except Exception as e:
        logger.error(f"❌ Day planning test failed: {e}", exc_info=True)
        return []

async def test_multi_category_search():
    """Test searching across multiple activity categories."""
    logger.info("\n" + "=" * 60)
    logger.info("Testing Multi-Category Search")
    logger.info("=" * 60)
    
    categories = ["swimming", "gymnastics", "dance", "art", "sports"]
    all_results = []
    category_counts = {}
    
    try:
        filters = {
            "min_age": 5,
            "max_age": 5,
            "location": "New Westminster"
        }
        qdrant_filter = _create_qdrant_filter(filters)
        
        for category in categories:
            logger.info(f"🔍 Searching for {category} activities...")
            results = await _perform_advanced_search(
                query=category,
                qdrant_filter=qdrant_filter,
                limit=20
            )
            
            category_counts[category] = len(results)
            all_results.extend(results)
            logger.info(f"   Found {len(results)} {category} activities")
        
        logger.info(f"\n✅ Total unique activities found: {len(set(r.get('record_id', '') for r in all_results))}")
        logger.info("📊 Category breakdown:")
        for cat, count in category_counts.items():
            logger.info(f"   - {cat}: {count} activities")
            
        return all_results
        
    except Exception as e:
        logger.error(f"❌ Multi-category search failed: {e}", exc_info=True)
        return []

async def test_burnaby_search():
    """Test searching in Burnaby."""
    logger.info("\n" + "=" * 60)
    logger.info("Testing Burnaby Search")
    logger.info("=" * 60)
    
    try:
        filters = {
            "min_age": 7,
            "max_age": 7,
            "location": "Burnaby"
        }
        
        qdrant_filter = _create_qdrant_filter(filters)
        results = await _perform_advanced_search(
            query="activities",
            qdrant_filter=qdrant_filter,
            limit=30
        )
        
        logger.info(f"✅ Found {len(results)} activities for 7-year-olds in Burnaby")
        
        # Show facility distribution
        facilities = {}
        for activity in results:
            facility = activity.get("facility", "Unknown")
            facilities[facility] = facilities.get(facility, 0) + 1
        
        logger.info("🏢 Top facilities:")
        for facility, count in sorted(facilities.items(), key=lambda x: x[1], reverse=True)[:5]:
            logger.info(f"   - {facility}: {count} activities")
            
        return results
        
    except Exception as e:
        logger.error(f"❌ Burnaby search failed: {e}", exc_info=True)
        return []

async def main():
    """Run all tests."""
    logger.info("🚀 Starting Core Functionality Tests")
    logger.info("Testing back-to-back search without ADK dependencies\n")
    
    # Run tests
    qdrant_results = await test_qdrant_search()
    day_planning_results = await test_day_planning()
    multi_category_results = await test_multi_category_search()
    burnaby_results = await test_burnaby_search()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 Test Summary")
    logger.info("=" * 60)
    
    tests_passed = 0
    tests_total = 4
    
    if qdrant_results:
        tests_passed += 1
        logger.info("✅ Direct Qdrant search: PASSED")
    else:
        logger.info("❌ Direct Qdrant search: FAILED")
        
    if day_planning_results:
        tests_passed += 1
        logger.info("✅ Day planning (back-to-back): PASSED")
    else:
        logger.info("❌ Day planning (back-to-back): FAILED")
        
    if multi_category_results:
        tests_passed += 1
        logger.info("✅ Multi-category search: PASSED")
    else:
        logger.info("❌ Multi-category search: FAILED")
        
    if burnaby_results:
        tests_passed += 1
        logger.info("✅ Burnaby search: PASSED")
    else:
        logger.info("❌ Burnaby search: FAILED")
    
    logger.info(f"\n🏁 Tests passed: {tests_passed}/{tests_total}")
    
    if tests_passed == tests_total:
        logger.info("🎉 All tests passed! The core search functionality is working.")
    else:
        logger.info("⚠️ Some tests failed. Check the logs above for details.")
    
    # Show final recommendation
    if day_planning_results:
        logger.info("\n💡 Back-to-back search is working! Found opportunities like:")
        for pair in day_planning_results[:2]:
            first = pair.get('first_class', {})
            second = pair.get('second_class', {})
            logger.info(f"   • {first.get('name', 'Unknown')} → {second.get('name', 'Unknown')}")
            logger.info(f"     Gap: {pair.get('gap_description', 'Unknown gap')}")

if __name__ == "__main__":
    asyncio.run(main())
