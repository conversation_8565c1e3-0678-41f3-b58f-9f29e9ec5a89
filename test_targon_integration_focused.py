"""
Test Targon client integration with rate limiter (focused test)
"""

import asyncio
import os
import sys
import logging
import time
from dotenv import load_dotenv

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Import individual components to avoid full module loading
def test_imports():
    """Test that we can import the necessary components."""
    try:
        # Test Targon client import
        from multi_tool_agent.utils.targon_client import TargonClient
        logger.info("✅ Successfully imported TargonClient")
        
        # Test rate limiter import
        from multi_tool_agent.utils.rate_limiter import RateLimitConfig, KimiRateLimiter
        logger.info("✅ Successfully imported rate limiter components")
        
        # Test config import
        from multi_tool_agent.config import AgentConfig
        logger.info("✅ Successfully imported AgentConfig")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_rate_limiter():
    """Test the rate limiter functionality."""
    try:
        from multi_tool_agent.utils.rate_limiter import RateLimitConfig, KimiRateLimiter
        
        # Create a rate limiter with very short intervals for testing
        config = RateLimitConfig(
            rpm=2,  # 2 requests per minute for testing
            min_interval=1.0,  # 1 second minimum interval
            max_queue_size=10,
            queue_timeout=5.0,
            enable_fallback=True
        )
        
        limiter = KimiRateLimiter(config)
        logger.info("✅ Created rate limiter")
        
        # Test 1: First request should succeed immediately
        start_time = time.time()
        async with limiter.acquire():
            logger.info("✅ First request acquired immediately")
            await asyncio.sleep(0.1)  # Simulate work
        
        # Test 2: Second request should wait due to min_interval
        async with limiter.acquire():
            elapsed = time.time() - start_time
            logger.info(f"✅ Second request acquired after {elapsed:.2f}s")
            await asyncio.sleep(0.1)  # Simulate work
        
        # Test 3: Test rate limit detection
        is_limited = await limiter.is_rate_limited()
        logger.info(f"✅ Rate limit status: {is_limited}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Rate limiter test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_hybrid_manager():
    """Test the hybrid model manager."""
    try:
        from multi_tool_agent.utils.rate_limiter import HybridModelManager
        
        # Create hybrid manager
        manager = HybridModelManager()
        logger.info("✅ Created hybrid model manager")
        
        # Test model selection
        model = manager.get_model_for_task("reasoning", is_critical=True)
        logger.info(f"✅ Selected model for reasoning: {model}")
        
        # Test should_use_kimi
        should_use = await manager.should_use_kimi("reasoning", is_critical=True)
        logger.info(f"✅ Should use Kimi for reasoning: {should_use}")
        
        # Test DeepInfra configuration
        logger.info(f"✅ DeepInfra enabled: {manager.use_deepinfra}")
        if manager.use_deepinfra:
            logger.info(f"✅ DeepInfra model: {manager.deepinfra_kimi_model}")
            logger.info(f"✅ DeepInfra API base: {manager.deepinfra_api_base}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Hybrid manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_targon_integration():
    """Test full Targon integration."""
    try:
        from multi_tool_agent.utils.targon_client import get_targon_client
        from multi_tool_agent.utils.rate_limiter import get_hybrid_manager
        
        # Get components
        client = get_targon_client()
        manager = get_hybrid_manager()
        
        logger.info("✅ Got Targon client and hybrid manager")
        
        # Test with rate limiter
        if manager.use_deepinfra:
            async with manager.deepinfra_limiter.acquire():
                logger.info("✅ Acquired Targon rate limiter")
                
                # Test streaming
                messages = [{"role": "user", "content": "Say 'Integration test successful!' to confirm."}]
                
                response_text = ""
                async for chunk in client.stream_completion(
                    model=manager.targon_kimi_model,
                    messages=messages,
                    temperature=0.7
                ):
                    response_text += chunk
                    print(chunk, end="", flush=True)
                
                print()  # New line
                logger.info(f"✅ Response: {response_text}")
                
                return True
        else:
            logger.warning("⚠️ Targon not enabled")
            return False
        
    except Exception as e:
        logger.error(f"❌ Targon integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    logger.info("🚀 Starting focused Targon integration tests...")
    
    # Test 1: Imports
    logger.info("\n=== Test 1: Component Imports ===")
    success1 = test_imports()
    
    if not success1:
        logger.error("❌ Import test failed, stopping")
        return
    
    # Test 2: Rate limiter
    logger.info("\n=== Test 2: Rate Limiter ===")
    success2 = await test_rate_limiter()
    
    # Test 3: Hybrid manager
    logger.info("\n=== Test 3: Hybrid Manager ===")
    success3 = await test_hybrid_manager()
    
    # Test 4: Full integration
    logger.info("\n=== Test 4: Full Targon Integration ===")
    success4 = await test_targon_integration()
    
    # Summary
    logger.info("\n=== Test Summary ===")
    logger.info(f"Component Imports: {'✅ PASSED' if success1 else '❌ FAILED'}")
    logger.info(f"Rate Limiter: {'✅ PASSED' if success2 else '❌ FAILED'}")
    logger.info(f"Hybrid Manager: {'✅ PASSED' if success3 else '❌ FAILED'}")
    logger.info(f"Targon Integration: {'✅ PASSED' if success4 else '❌ FAILED'}")
    
    if success1 and success2 and success3 and success4:
        logger.info("🎉 All tests passed! Targon integration is working correctly.")
    else:
        logger.error("⚠️ Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
