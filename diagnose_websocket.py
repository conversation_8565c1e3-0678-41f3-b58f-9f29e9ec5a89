"""
Diagnostic script to trace WebSocket issues
"""
import asyncio
import logging
import sys
import traceback
from adk_server import app, runner, session_service, APP_NAME
from google.genai.types import Part, Content
from fastapi.testclient import TestClient
import json

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stdout
)

# Suppress some noisy loggers
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)

logger = logging.getLogger(__name__)

async def simulate_websocket_handler():
    """Simulate what happens in the WebSocket handler"""
    logger.info("🔍 Simulating WebSocket handler logic...")
    
    try:
        # Simulate creating a session
        user_id = "test_user"
        frontend_session_id = "test_session"
        
        logger.info("📋 Creating ADK session...")
        adk_session = await session_service.create_session(app_name=APP_NAME, user_id=user_id)
        effective_adk_session_id = adk_session.id
        logger.info(f"✅ Created ADK session: {effective_adk_session_id}")
        
        # Simulate message content
        content = Content(
            parts=[Part(text="What swimming classes are available for kids?")],
            role="user"
        )
        
        logger.info("🚀 Running agent...")
        event_count = 0
        
        try:
            # This is what the WebSocket handler does
            async for adk_event in runner.run_async(
                new_message=content,
                user_id=user_id,
                session_id=effective_adk_session_id,
            ):
                event_count += 1
                logger.info(f"📥 Event #{event_count}: {type(adk_event)}")
                
                # Log event details
                if hasattr(adk_event, 'partial'):
                    logger.debug(f"   Partial: {adk_event.partial}")
                if hasattr(adk_event, 'content'):
                    content_str = str(adk_event.content)
                    if len(content_str) > 200:
                        content_str = content_str[:200] + "..."
                    logger.debug(f"   Content preview: {content_str}")
                
                # Check for final response
                if hasattr(adk_event, 'is_final_response') and callable(adk_event.is_final_response):
                    is_final = adk_event.is_final_response()
                    logger.debug(f"   Is final response: {is_final}")
                    if is_final:
                        logger.info("🏁 Received final response")
                        break
                        
                # Stop after reasonable number of events
                if event_count >= 10:
                    logger.info("⚠️ Stopping after 10 events to prevent overflow")
                    break
                    
        except Exception as e:
            logger.error(f"❌ Error during agent execution: {type(e).__name__}: {str(e)}")
            logger.error(f"Traceback:\n{traceback.format_exc()}")
            raise
            
        logger.info(f"✅ Agent completed successfully with {event_count} events")
        
    except Exception as e:
        logger.error(f"❌ Top-level error: {type(e).__name__}: {str(e)}")
        logger.error(f"Traceback:\n{traceback.format_exc()}")

def test_websocket_endpoint():
    """Test the actual WebSocket endpoint"""
    logger.info("\n🧪 Testing actual WebSocket endpoint...")
    
    with TestClient(app) as client:
        try:
            with client.websocket_connect("/ws") as websocket:
                logger.info("✅ WebSocket connected")
                
                # Send test message
                message = {
                    "content": "What swimming classes are available for kids?",
                    "session_id": "test_session",
                    "user_id": "test_user",
                    "type": "chat_message"
                }
                
                logger.info(f"📤 Sending: {json.dumps(message, indent=2)}")
                websocket.send_json(message)
                
                # Receive responses
                response_count = 0
                while True:
                    try:
                        data = websocket.receive_json(timeout=10)
                        response_count += 1
                        logger.info(f"📥 Response #{response_count}: {json.dumps(data, indent=2)}")
                        
                        if data.get("type") == "turn_complete":
                            logger.info("✅ Turn complete")
                            break
                        elif data.get("type") == "error":
                            logger.error(f"❌ Server error: {data}")
                            break
                            
                    except Exception as timeout_error:
                        logger.warning(f"⏱️ Timeout or connection closed: {timeout_error}")
                        break
                        
        except Exception as e:
            logger.error(f"❌ WebSocket test failed: {type(e).__name__}: {str(e)}")
            logger.error(f"Traceback:\n{traceback.format_exc()}")

async def main():
    """Run diagnostics"""
    logger.info("🚀 Starting WebSocket diagnostics...")
    
    # First test the agent logic in isolation
    await simulate_websocket_handler()
    
    # Then test the actual WebSocket endpoint
    test_websocket_endpoint()
    
    # Check API stats
    logger.info("\n📊 Checking API stats...")
    try:
        from multi_tool_agent.utils.api_monitor import get_api_monitor
        monitor = get_api_monitor()
        stats = monitor.get_current_stats()
        logger.info(f"Total API calls: {stats['total_calls']}")
        logger.info(f"Kimi calls: {stats['kimi_calls']}")
    except Exception as e:
        logger.error(f"Failed to get API stats: {e}")

if __name__ == "__main__":
    asyncio.run(main())
