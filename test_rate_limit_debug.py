#!/usr/bin/env python3
"""
Debug script to understand why rate limiting is not working.
"""

import asyncio
import logging
from multi_tool_agent.models.rate_limited_llm import create_ultra_optimized_model, RateLimitedLiteLlm
from multi_tool_agent.utils.rate_limiter import get_hybrid_manager
from multi_tool_agent.config import AgentConfig
from google.genai.types import Content, Part
from google.adk.models.lite_llm import LiteLlm

# Enable debug logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_rate_limited_model():
    """Test if RateLimitedLiteLlm is working correctly."""
    print("\n🧪 Testing RateLimitedLiteLlm directly...")
    
    # Create a rate-limited model
    model = RateLimitedLiteLlm(
        model=AgentConfig.KIMI_MODEL,
        task_type="test",
        is_critical=True
    )
    
    print(f"Created model: {type(model)}")
    print(f"Model name: {model.model}")
    print(f"Task type: {model._task_type}")
    print(f"Is critical: {model._is_critical}")
    
    # Check if it has the rate limiting methods
    print(f"\nHas generate_content_async: {hasattr(model, 'generate_content_async')}")
    print(f"Method type: {type(getattr(model, 'generate_content_async', None))}")
    
    # Check parent class
    print(f"\nParent classes: {[c.__name__ for c in type(model).__mro__]}")
    
    return model

async def test_ultra_optimized_model():
    """Test the ultra-optimized model creation."""
    print("\n🧪 Testing create_ultra_optimized_model...")
    
    model = create_ultra_optimized_model()
    
    print(f"Created model: {type(model)}")
    print(f"Model name: {model.model}")
    
    if isinstance(model, RateLimitedLiteLlm):
        print("✅ Model is RateLimitedLiteLlm")
        print(f"Task type: {model._task_type}")
        print(f"Is critical: {model._is_critical}")
    else:
        print("❌ Model is NOT RateLimitedLiteLlm!")
    
    return model

async def test_method_calls():
    """Test which methods are actually called by ADK."""
    print("\n🧪 Testing method calls...")
    
    class DebugLiteLlm(LiteLlm):
        """Debug wrapper to see which methods are called."""
        
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            print(f"DebugLiteLlm.__init__ called with model={kwargs.get('model')}")
        
        async def generate_content_async(self, *args, **kwargs):
            print(f"DebugLiteLlm.generate_content_async called!")
            print(f"Args: {args}")
            print(f"Kwargs: {kwargs}")
            async for response in super().generate_content_async(*args, **kwargs):
                yield response
        
        def generate_content(self, *args, **kwargs):
            print(f"DebugLiteLlm.generate_content called!")
            print(f"Args: {args}")
            print(f"Kwargs: {kwargs}")
            return super().generate_content(*args, **kwargs)
        
        def __getattribute__(self, name):
            if name.startswith('generate') or name.startswith('_generate'):
                print(f"Accessing method: {name}")
            return super().__getattribute__(name)
    
    model = DebugLiteLlm(model="gemini-2.5-flash")
    print(f"\nCreated debug model: {type(model)}")
    
    # Try to call it like ADK would
    try:
        content = Content(parts=[Part(text="Hello")], role="user")
        print("\nTrying to generate content...")
        
        # Test synchronous method
        if hasattr(model, 'generate_content'):
            print("Has generate_content method")
            # Don't actually call it to avoid API usage
        
        # Test async method
        if hasattr(model, 'generate_content_async'):
            print("Has generate_content_async method")
            # Don't actually call it to avoid API usage
            
    except Exception as e:
        print(f"Error: {e}")

async def check_manager_state():
    """Check the state of the hybrid manager."""
    print("\n🧪 Checking hybrid manager state...")
    
    manager = get_hybrid_manager()
    
    print(f"Kimi model: {manager.kimi_model}")
    print(f"DeepInfra model: {manager.deepinfra_kimi_model}")
    print(f"Use DeepInfra: {manager.use_deepinfra}")
    print(f"Fallback model: {manager.fallback_model}")
    print(f"Use hybrid: {manager.use_hybrid}")
    
    # Check rate limiter state
    print(f"\nKimi limiter last request: {manager.kimi_limiter.last_request_time}")
    print(f"Kimi limiter request count: {manager.kimi_limiter.request_count}")
    print(f"DeepInfra limiter last request: {manager.deepinfra_limiter.last_request_time}")

async def main():
    """Run all tests."""
    print("🚀 Starting rate limiting debug tests...")
    
    # Test 1: Check manager state
    await check_manager_state()
    
    # Test 2: Test rate-limited model
    model1 = await test_rate_limited_model()
    
    # Test 3: Test ultra-optimized model
    model2 = await test_ultra_optimized_model()
    
    # Test 4: Test method calls
    await test_method_calls()
    
    print("\n✅ Debug tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
