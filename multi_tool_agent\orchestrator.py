import logging
from google.adk.agents import Agent, SequentialAgent
from google.adk.models.lite_llm import LiteLlm
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List

# Corrected import path for schemas
from .tools.schemas import ActivityFilters
from .tools import <PERSON>Tool, KimiAgentTool, raw_activity_search, user_confirmation_tool
from .tools.intelligent_activity_tools import discover_activity_patterns, get_activity_summary, check_activity_availability, plan_full_day_schedule, find_multi_age_activities, find_concurrent_activities
from .tools.qdrant_adk_tools import qdrant_flexible_search, qdrant_get_collection_schema, qdrant_multi_facility_search
from .tools.enhanced_activity_orchestrator import find_comprehensive_back_to_back_activities
from .config import AgentConfig
from .models.rate_limited_llm import (
    create_reasoning_model,
    create_search_model,
    create_tool_model,
    create_response_model,
    create_simple_model,
    create_ultra_optimized_model
)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Pydantic Schemas ---
class SearchArgs(BaseModel):
    query: str
    filters: ActivityFilters
    find_back_to_back: bool

class SynthesizerInput(BaseModel):
    user_request: str
    raw_search_results: List[Dict[str, Any]]

# --- Query Classification Schema ---
class QueryClassification(BaseModel):
    is_simple: bool = Field(description="True if this is a simple, direct search query")
    confidence: float = Field(description="Confidence level (0.0-1.0) in the classification")
    reasoning: str = Field(description="Brief explanation of the classification")

# --- Specialist Agents ---
QueryClassifierAgent = Agent(
    name="QueryClassifierAgent",
    model=LiteLlm(model=AgentConfig.SEARCH_AGENT_MODEL),  # Use fallback for classification
    description="Determines if a user query is simple enough for fast-path processing.",
    instruction=(
        "You are a query complexity classifier. Analyze the user's request and determine if it's a simple, direct search that can be handled with a single search operation.\n\n"
        "**SIMPLE QUERIES (use fast path):**\n"
        "- Direct activity searches: 'swimming classes for kids', 'yoga classes', 'hockey for 8 year old'\n"
        "- Basic filtering: 'swimming in Burnaby', 'classes on Saturday'\n"
        "- Age-specific requests: 'activities for 5 year old'\n"
        "- Back-to-back requests: 'back to back classes', 'consecutive classes' (these are direct searches, not complex planning)\n\n"
        "**COMPLEX QUERIES (use full workflow):**\n"
        "- Multi-step planning: 'find activities that work with my schedule'\n"
        "- Comparison requests: 'which is better for my child'\n"
        "- Abstract planning: 'help me plan my child's week'\n"
        "- Complex scheduling: 'find activities that don't conflict with school'\n\n"
        "Respond with a JSON object containing:\n"
        "- `is_simple`: boolean (true if straightforward search, false if complex orchestration needed)\n"
        "- `confidence`: float (0.0-1.0 confidence level)\n"
        "- `reasoning`: string (brief explanation of the classification)"
    ),
)

# Token-Efficient Intelligent Search Agent
IntelligentSearchAgent = Agent(
    name="IntelligentSearchAgent",
    model=LiteLlm(model=AgentConfig.SEARCH_AGENT_MODEL),  # Use fallback for tool execution
    description="Uses intelligent tools to discover patterns and provide insights with minimal token usage.",
    instruction=(
        "You are an intelligent activity search agent that uses smart tools to analyze patterns efficiently.\n\n"
        "🎯 **CRITICAL: ALWAYS USE LOCAL DATABASE FIRST**\n"
        "- **NEVER use web search for New Westminster or Burnaby activities**\n"
        "- **ALWAYS use your specialized tools first** - they access our comprehensive local database\n"
        "- **Our database contains 15,000+ activities** from New Westminster and Burnaby\n"
        "- **Only suggest web search** if no results found in local database\n\n"
        "**AVAILABLE TOOLS (USE THESE FIRST):**\n"
        "1. **discover_activity_patterns**: Find patterns like back-to-back classes, conflicts, progressions\n"
        "2. **get_activity_summary**: Get concise summaries without heavy data processing\n"
        "3. **check_activity_availability**: Check specific availability efficiently\n"
        "4. **plan_full_day_schedule**: Create comprehensive day schedules with intelligent gap planning\n"
        "5. **qdrant_multi_facility_search**: Search across multiple facilities for variety\n\n"
        "**SEARCH PRIORITY:**\n"
        "1. 🥇 **First**: Use your tools to search local database\n"
        "2. 🥈 **Only if no results**: Mention web search as backup\n\n"
        "**STRATEGY:**\n"
        "- For pattern requests (back-to-back, consecutive): Use discover_activity_patterns with pattern_types=['back_to_back']\n"
        "- For general searches: Use get_activity_summary with appropriate summary_type\n"
        "- For specific availability: Use check_activity_availability\n\n"
        "**EXAMPLES:**\n"
        "User: 'back to back classes for 5 year olds in new west'\n"
        "Call: discover_activity_patterns(query='classes', filters={'age': 5, 'location': 'new west'}, pattern_types=['back_to_back'])\n\n"
        "User: 'swimming classes for kids'\n"
        "Call: get_activity_summary(query='swimming classes', filters={'age': 5}, summary_type='quick')\n\n"
        "**RESPONSE FORMAT:**\n"
        "- Present insights clearly and concisely\n"
        "- Focus on actionable information\n"
        "- Include key details: times, dates, facilities, prices\n"
        "- Provide helpful next steps"
    ),
    tools=[
        discover_activity_patterns,
        get_activity_summary,
        check_activity_availability,
        plan_full_day_schedule,
        find_multi_age_activities,
        find_concurrent_activities,
        qdrant_multi_facility_search
    ],
    output_key="intelligent_search_results",
    disallow_transfer_to_parent=True,
    disallow_transfer_to_peers=True
)

# Keep original FastSearchAgent as fallback
FastSearchAgent = Agent(
    name="FastSearchAgent",
    model=LiteLlm(model=AgentConfig.SEARCH_AGENT_MODEL),  # Use fallback for simple searches
    description="Fallback agent for simple searches when intelligent tools aren't needed.",
    instruction=(
        "You are a simple fallback search agent. Use raw_activity_search for basic queries.\n\n"
        "🎯 **CRITICAL: ALWAYS USE LOCAL DATABASE FIRST**\n"
        "- **NEVER use web search for New Westminster or Burnaby activities**\n"
        "- **ALWAYS use raw_activity_search first** - it accesses our comprehensive local database\n"
        "- **Our database contains 15,000+ activities** from New Westminster and Burnaby\n"
        "- **Only suggest web search** if no results found in local database\n\n"
        "Keep responses concise and focused."
    ),
    tools=[raw_activity_search],
    output_key="fast_search_results",
    disallow_transfer_to_parent=True,
    disallow_transfer_to_peers=True
)

# --- Kimi K2 Powered Agent ---
KimiIntelligentAgent = Agent(
    name="KimiIntelligentAgent",
    model=LiteLlm(model=AgentConfig.KIMI_MODEL),
    description="Advanced reasoning agent powered by Kimi K2 for complex activity analysis and long-context understanding.",
    instruction=(
        "You are an advanced AI assistant powered by the Kimi K2 model, specializing in complex reasoning and long-context understanding. "
        "You excel at analyzing detailed activity information and providing comprehensive, well-structured responses.\n\n"
        "🎯 **CRITICAL: ALWAYS USE LOCAL DATABASE FIRST**\n"
        "- **NEVER use web search for New Westminster or Burnaby activities**\n"
        "- **ALWAYS use your specialized tools first** - they access our comprehensive local database\n"
        "- **Our database contains 15,000+ activities** from New Westminster and Burnaby\n"
        "- **Only suggest web search** if no results found in local database\n\n"
        "**YOUR CAPABILITIES:**\n"
        "- Advanced pattern recognition and analysis\n"
        "- Long-context understanding for complex queries\n"
        "- Detailed reasoning and explanation\n"
        "- Comprehensive activity planning and recommendations\n\n"
        "**AVAILABLE TOOLS (USE THESE FIRST):**\n"
        "1. **discover_activity_patterns**: Find complex patterns, relationships, and scheduling conflicts\n"
        "2. **get_activity_summary**: Generate detailed summaries with advanced analysis\n"
        "3. **check_activity_availability**: Perform sophisticated availability checks\n"
        "4. **plan_full_day_schedule**: Create comprehensive day schedules with intelligent gap planning\n"
        "5. **qdrant_flexible_search**: Direct Qdrant queries for custom searches\n"
        "6. **qdrant_get_collection_schema**: Understand available fields for filtering\n"
        "7. **qdrant_multi_facility_search**: Search across multiple facilities for variety\n\n"
        "**SEARCH PRIORITY:**\n"
        "1. 🥇 **First**: Use your tools to search local database\n"
        "2. 🥈 **Only if no results**: Mention web search as backup\n\n"
        "**RESPONSE STRATEGY:**\n"
        "- Provide detailed, well-reasoned responses\n"
        "- Include comprehensive analysis and insights\n"
        "- Offer multiple options and alternatives when appropriate\n"
        "- Explain reasoning behind recommendations\n"
        "- Consider complex scheduling and logistical factors\n\n"
        "**EXAMPLES OF COMPLEX QUERIES YOU HANDLE:**\n"
        "- Multi-child scheduling coordination\n"
        "- Complex pattern analysis (progression paths, skill development)\n"
        "- Detailed comparison and recommendation requests\n"
        "- Long-term planning and scheduling optimization\n\n"
        "Always provide thorough, helpful responses that demonstrate your advanced reasoning capabilities."
    ),
    tools=[
        discover_activity_patterns,
        get_activity_summary,
        check_activity_availability,
        plan_full_day_schedule,
        find_multi_age_activities,
        qdrant_flexible_search,
        qdrant_get_collection_schema,
        qdrant_multi_facility_search
    ],
    output_key="kimi_intelligent_results",
    disallow_transfer_to_parent=True,
    disallow_transfer_to_peers=True
)

# --- Specialist Agents ---
SearchPlannerAgent = Agent(
    name="SearchPlannerAgent",
    model=LiteLlm(model=AgentConfig.ORCHESTRATOR_MODEL),
    description="Analyzes a user's request and creates a step-by-step plan for how it will find the answer using its internal tools.",
    instruction=(
        "You are an expert activity planning assistant. Your job is to create a concise, 1-2 sentence, human-readable plan that describes the actions YOU WILL TAKE using your internal tools. "
        "Do NOT suggest external actions like 'search online' or 'contact facilities'. "
        "Your plan must state that you will search your specialized activity database.\n\n"
        "🎯 **CRITICAL: ALWAYS MENTION LOCAL DATABASE**\n"
        "- **Always state you will search our comprehensive local database first**\n"
        "- **Our database contains 15,000+ activities** from New Westminster and Burnaby\n"
        "- **Never mention web search** in your initial plan\n\n"
        "--- CORRECT EXAMPLE ---\n"
        "User Request: 'any swimming classes for 5 year olds in new west?'\n"
        "Your Plan: 'I will search our comprehensive local activity database containing 15,000+ activities for swimming lessons suitable for a 5-year-old in New Westminster. Does that sound good?'\n\n"
        "--- INCORRECT EXAMPLE ---\n"
        "Plan: '1. Search online... 2. Check websites...'\n\n"
        "Your output is ONLY the single-paragraph plan text to be shown to the user for approval."
    ),
    # This agent should NOT have an output_key. Its return value will be captured by the AgentTool.
)

ArgumentGeneratorAgent = Agent(
    name="ArgumentGeneratorAgent",
    model=LiteLlm(model=AgentConfig.ORCHESTRATOR_MODEL),
    description="Generates the precise JSON arguments for the `raw_activity_search` tool.",
    instruction=(
        "You are an expert at creating structured search queries. Based on the user's request, generate a single JSON object with the exact arguments (`query`, `filters`, `find_back_to_back`) for the `raw_activity_search` tool.\n\n"
        "**CRITICAL REASONING RULES:**\n"
        "1. **General Age-Based Queries:** For general requests like 'swimming classes for 5 year old' or 'any swimming classes for kids':\n"
        "   - Use a broad query like 'swimming classes' without forcing 'beginner' terms\n"
        "   - Only set age filter, let the search return all age-appropriate options\n"
        "   - Do NOT set name_contains filter unless user specifically mentions level/difficulty\n\n"
        "2. **Explicit Beginner Requests:** Only when user explicitly mentions 'beginner', 'intro', 'starter', 'first time':\n"
        "   - Add 'beginner introduction starter' to the query\n"
        "   - Set `filters.name_contains` to 'beginner intro starter level 1'\n\n"
        "3. **Level-Specific Requests:** If the user asks for a specific level (e.g., 'level 5', 'advanced'):\n"
        "   - Include that exact level in the `query` string\n"
        "   - Set `filters.name_contains` to match that level (e.g., 'level 5' or 'advanced')\n\n"
        "4. **Back-to-Back Detection:** Set `find_back_to_back` to `true` only if the user explicitly mentions 'back-to-back', 'consecutive', 'one after another', or similar phrasing.\n\n"
        "5. **Filter Extraction:** Extract explicit filters (age, location, day_of_week, max_price, date, is_open) from the user's request.\n\n"
        "**EXAMPLES:**\n"
        "User: 'swimming classes for 5 year old'\n"
        "Response: {\"query\": \"swimming classes\", \"filters\": {\"age\": 5}, \"find_back_to_back\": false}\n\n"
        "User: 'beginner swimming classes for 5 year old'\n"
        "Response: {\"query\": \"swimming classes beginner introduction starter\", \"filters\": {\"age\": 5, \"name_contains\": \"beginner intro starter level 1\"}, \"find_back_to_back\": false}\n\n"
        "User: 'level 5 swimming classes'\n"
        "Response: {\"query\": \"swimming classes level 5\", \"filters\": {\"name_contains\": \"level 5\"}, \"find_back_to_back\": false}\n\n"
        "Respond with only the JSON object, no additional text."
    ),
)

ActivitySynthesizerAgent = Agent(
    name="ActivitySynthesizerAgent",
    model=LiteLlm(model=AgentConfig.RESPONSE_AGENT_MODEL),
    description="Takes raw JSON search data and synthesizes a high-quality answer.",
    instruction=(
        "You are an expert parent activity consultant. You will be given raw activity data in JSON format and the user's original request. "
        "Create a helpful, well-structured response that organizes activities by level/type and includes detailed scheduling information.\n\n"
        "**CRITICAL FORMATTING REQUIREMENTS:**\n"
        "1. **Start with a positive confirmation**: 'Yes, there are several [activity type] available for [age]-year-olds in [location].'\n"
        "2. **Group by Level/Type**: Organize activities by level (e.g., 'Swimming Level 01 - Preschool (Ages 4-6)')\n"
        "3. **For each level, show**:\n"
        "   - Level name with age range in parentheses (use min_age_years and max_age_years from data)\n"
        "   - Location: [Facility name]\n"
        "   - Schedule: Every [days] from [start time] - [end time] ([start date] - [end date], [year]) - $[price]\n"
        "   - Registration: [activity_url] (if available)\n"
        "4. **Age range formatting**: Use min_age_years and max_age_years from the data. If max_age_years >= 99, show as '[min_age]+'.\n"
        "5. **Time formatting**: Convert 24-hour to 12-hour format (e.g., 16:00 → 4:00 PM)\n"
        "6. **Date formatting**: Convert YYYY-MM-DD to 'Mon DD - Mon DD, YYYY' format\n"
        "7. **Days formatting**: Convert ['monday', 'wednesday'] to 'Mon, Wed'\n"
        "8. **Price handling**: If price is missing/null, estimate based on similar activities or omit price\n"
        "9. **Include registration links**: Always include activity_url when available for easy registration\n"
        "        "10. **Clean response format**: Never include tool call artifacts, XML tags, or system messages in your response\n"
        "11. **CRITICAL**: Do not include any text like <|tool_calls_section_end|> or similar artifacts in your response\n"
        "12. **Response only**: Provide only the final user-facing response, no internal processing text\n\n"\n\n"
        "**EXACT EXAMPLE FORMAT TO FOLLOW:**\n"
        "Swimming Level 01 - Preschool (Ages 4-6)\n"
        "Location: təməsew̓txʷ Aquatic and Community Centre\n"
        "Schedule: Every Mon, Wed from 4:00 PM - 4:25 PM (Jun 30 - Jul 28, 2025) - $72.00\n"
        "Schedule: Every Tue, Thu from 4:00 PM - 4:25 PM (Jul 03 - Jul 29, 2025) - $64.00\n"
        "Registration: [activity_url_if_available]\n\n"
        "**BACK-TO-BACK HANDLING**: If the search results contain 'back_to_back_pairs', prioritize these in your response:\n"
        "- Start with: 'Yes, there are several back-to-back class options for [age]-year-olds at [facility].'\n"
        "- For each pair, show: 'Class 1 ([time]) → Class 2 ([time]) with [gap] minute break'\n"
        "- Include detailed scheduling for each class in the pair\n"
        "- Group pairs by facility and date for clarity\n\n"
        "**IMPORTANT**: Use the exact formatting style shown above. Convert all times, dates, and organize by level systematically."
    ),
    input_schema=SynthesizerInput,
)

# --- NEW CONTROLLER AGENT TO MANAGE THE ENTIRE SUB-WORKFLOW ---
# This single agent will handle the entire plan -> confirm -> ... -> synthesize flow.
ActivityPlanAndSearchController = Agent(
    name="ActivityPlanAndSearchController",
    model=LiteLlm(model=AgentConfig.ORCHESTRATOR_MODEL),
    description="Manages the entire workflow of planning, confirming, searching, and synthesizing a response for an activity request.",
    instruction=(
        "You are the main controller for finding activities. You MUST follow these steps in order:\n"
        "1. **Call `SearchPlannerAgent`** with the user's request to get a human-readable `plan`.\n"
        "2. **Call `user_confirmation_tool`** with the `plan` to get user approval. If the user says 'no' or disagrees, stop and apologize.\n"
        "3. **Call `ArgumentGeneratorAgent`** with the user's request to get the precise `tool_arguments`.\n"
        "4. **Call `raw_activity_search`** using the `tool_arguments` from the previous step. This will give you the `raw_search_results`.\n"
        "5. **Call `ActivitySynthesizerAgent`** with the original `user_request` and the `raw_search_results` to generate the `final_answer`.\n"
        "6. **Return the `final_answer`** as your result."
    ),
    tools=[
        # Expose all necessary components as tools to this controller
        KimiAgentTool(agent=SearchPlannerAgent),
        user_confirmation_tool,
        KimiAgentTool(agent=ArgumentGeneratorAgent),
        raw_activity_search,
        KimiAgentTool(agent=ActivitySynthesizerAgent)
    ],
    output_key="final_formatted_answer" # The final result of this entire flow
)

# --- WORKFLOW DEFINITIONS (Simplified) ---

# The main activity workflow is now just the single, powerful controller agent.
ActivityWorkflow = SequentialAgent(
    name="ActivityWorkflow",
    sub_agents=[
        ActivityPlanAndSearchController
    ]
)

# --- (The rest of the file: GeneralInfoAgent, ResponseAgent, OrchestratorAgent, root_agent remains the same) ---
GeneralInfoAgent = Agent(
    name="GeneralInfoAgent",
    model=LiteLlm(model=AgentConfig.SEARCH_AGENT_MODEL),
    description="Answers general questions by searching the web.",
    instruction=(
        "You are a helpful assistant that answers general questions. "
        "For questions that require current information or web search, "
        "respond with a helpful answer based on your knowledge. "
        "If you need to search for current information, say so and provide what you can from your training data."
    ),
    output_key="general_info_results",
    disallow_transfer_to_parent=True,
    disallow_transfer_to_peers=True
)

ResponseAgent = Agent(
    name="ResponseAgent",
    model=LiteLlm(model=AgentConfig.RESPONSE_AGENT_MODEL),
    description="Presents the final answer to the user.",
    instruction=(
        "You are the final response agent. Your job is to present the information found by other agents to the user in a clear and friendly manner. "
        "Check the session state for 'final_formatted_answer' or 'general_info_results'. "
        "Present the content of whichever key is available exactly as it is without adding any conversational filler."
    )
)

SmartOrchestratorAgent = Agent(
    name="parent_activity_assistant_smart_orchestrator",
    model=LiteLlm(model=AgentConfig.ORCHESTRATOR_MODEL),
    description="A smart dispatcher that uses fast-path optimization and advanced reasoning capabilities.",
    instruction=(
        "You are an intelligent dispatcher with fast-path optimization and access to advanced AI models. Follow this process:\n\n"
        "🎯 **CRITICAL: ALL AGENTS PRIORITIZE LOCAL DATABASE**\n"
        "- **All your sub-agents use our comprehensive local database first**\n"
        "- **Our database contains 15,000+ activities** from New Westminster and Burnaby\n"
        "- **Never suggest web search** for New Westminster or Burnaby activities\n\n"
        "1. **For general questions** (not about finding activities): delegate to 'GeneralInfoAgent'\n\n"
        "2. **For activity searches**: \n"
        "   a. **Call `QueryClassifierAgent`** to determine if the query is simple or complex\n"
        "   b. **If simple** (`is_simple` = true): delegate to 'IntelligentSearchAgent' for efficient pattern discovery\n"
        "   c. **If complex** (`is_simple` = false): \n"
        "      - For highly complex reasoning, multi-step planning, or detailed analysis: delegate to 'KimiIntelligentAgent'\n"
        "      - For standard complex workflows: delegate to 'ActivityWorkflow'\n\n"
        "**ROUTING GUIDELINES:**\n"
        "- **IntelligentSearchAgent**: Simple, direct searches (swimming classes, back-to-back classes)\n"
        "- **KimiIntelligentAgent**: Complex reasoning, multi-child coordination, detailed analysis, long-context queries\n"
        "- **ActivityWorkflow**: Standard complex workflows that need step-by-step orchestration\n"
        "- **GeneralInfoAgent**: Non-activity related questions\n\n"
        "**EXAMPLES:**\n"
        "- 'swimming for 5 year old' → Simple → IntelligentSearchAgent\n"
        "- 'back to back classes' → Simple → IntelligentSearchAgent\n"
        "- 'coordinate activities for 3 kids with different ages and schedules' → Complex → KimiIntelligentAgent\n"
        "- 'detailed analysis of swimming progression paths' → Complex → KimiIntelligentAgent\n"
        "- 'standard activity search with confirmation' → Complex → ActivityWorkflow\n"
        "- 'what is the weather today?' → General → GeneralInfoAgent"
    ),
    tools=[
        KimiAgentTool(agent=QueryClassifierAgent)
    ],
    sub_agents=[
        IntelligentSearchAgent,
        KimiIntelligentAgent,
        ActivityWorkflow,
        GeneralInfoAgent
    ]
)

# --- OPTIMIZED SINGLE AGENT FOR LOW API USAGE ---
# This agent handles everything in one call to minimize API usage (10 RPM limit)
OptimizedSingleAgent = Agent(
    name="OptimizedActivityAgent",
    model=LiteLlm(model=AgentConfig.KIMI_MODEL),
    description="Single optimized agent that handles all activity searches with minimal API calls.",
    instruction=(
        "You are an optimized activity search assistant that handles all requests in a single interaction to minimize API usage.\n\n"
        "🎯 **CRITICAL: ALWAYS USE LOCAL DATABASE FIRST**\n"
        "- **NEVER use web search for New Westminster or Burnaby activities**\n"
        "- **ALWAYS use your specialized tools first** - they access our comprehensive local database\n"
        "- **Our database contains 15,000+ activities** from New Westminster and Burnaby\n"
        "- **Only suggest web search** if no results found in local database\n\n"
        "**AVAILABLE TOOLS (USE THESE FIRST):**\n"
        "1. **discover_activity_patterns**: Find patterns like back-to-back classes, conflicts, progressions\n"
        "2. **get_activity_summary**: Get concise summaries without heavy data processing\n"
        "3. **check_activity_availability**: Check specific availability efficiently\n\n"
        "**OPTIMIZATION STRATEGY:**\n"
        "- Handle the entire request in ONE interaction\n"
        "- Use tools efficiently to get comprehensive results\n"
        "- Provide complete, detailed responses immediately\n"
        "- No need for confirmation or multi-step workflows\n\n"
        "**RESPONSE FORMAT:**\n"
        "- Provide immediate, comprehensive answers\n"
        "- Include all relevant details: times, dates, facilities, prices\n"
        "- Offer multiple options when available\n"
        "- Be thorough and helpful in a single response"
    ),
    tools=[
        discover_activity_patterns,
        get_activity_summary,
        check_activity_availability
    ]
)

# --- OPTIMIZED MULTI-AGENT WORKFLOW FOR LOW API USAGE ---
# Streamlined workflow that skips unnecessary steps and combines operations

# Fast Direct Search Agent (skips classification and confirmation)
FastDirectSearchAgent = Agent(
    name="FastDirectSearchAgent",
    model=LiteLlm(model=AgentConfig.KIMI_MODEL),
    description="Direct search agent using Kimi K2 for intelligent tool calling and search decisions.",
    instruction=(
        "You are a direct activity search agent optimized for minimal API usage. Handle requests immediately without asking for confirmation.\n\n"
        "🎯 **CRITICAL: ALWAYS USE LOCAL DATABASE FIRST**\n"
        "- **NEVER use web search for New Westminster or Burnaby activities**\n"
        "- **ALWAYS use your specialized tools first** - they access our comprehensive local database\n"
        "- **Our database contains 15,000+ activities** from New Westminster and Burnaby\n\n"
        "**STRATEGY:**\n"
        "- Analyze the query and immediately use the most appropriate tool\n"
        "- For pattern requests (back-to-back, consecutive): Use discover_activity_patterns\n"
        "- For general searches: Use get_activity_summary\n"
        "- For availability checks: Use check_activity_availability\n"
        "- Provide complete, detailed responses immediately\n\n"
        "**NO CONFIRMATION NEEDED** - Just search and respond with comprehensive results."
    ),
    tools=[
        discover_activity_patterns,
        get_activity_summary,
        check_activity_availability
    ],
    output_key="direct_search_results"
)

# Create a separate GeneralInfoAgent for optimized workflow to avoid conflicts
OptimizedGeneralInfoAgent = Agent(
    name="OptimizedGeneralInfoAgent",
    model=LiteLlm(model=AgentConfig.SEARCH_AGENT_MODEL),
    description="Answers general questions by searching the web (optimized version).",
    instruction=(
        "You are a general information agent. Answer non-activity related questions by searching the web. "
        "Keep responses concise and helpful."
    ),
    tools=[],
    output_key="optimized_general_info_results",
    disallow_transfer_to_parent=True,
    disallow_transfer_to_peers=True
)

# Optimized Multi-Agent Orchestrator (2 API calls max)
OptimizedOrchestratorAgent = Agent(
    name="OptimizedOrchestratorAgent",
    model=LiteLlm(model=AgentConfig.KIMI_MODEL),
    description="Optimized orchestrator using Kimi K2 for intelligent agent delegation and routing.",
    instruction=(
        "You are an optimized orchestrator designed for minimal API usage (10 RPM limit). Route queries efficiently:\n\n"
        "🎯 **ROUTING STRATEGY (NO CLASSIFICATION NEEDED):**\n"
        "- **Activity searches**: Always delegate to 'FastDirectSearchAgent'\n"
        "- **General questions**: Delegate to 'OptimizedGeneralInfoAgent'\n\n"
        "**OPTIMIZATION RULES:**\n"
        "- Skip query classification - route directly based on keywords\n"
        "- Activity keywords: swimming, classes, activities, lessons, sports, programs, back-to-back, etc.\n"
        "- General keywords: weather, directions, contact, hours, etc.\n\n"
        "**EXAMPLES:**\n"
        "- 'swimming for 5 year old' → FastDirectSearchAgent\n"
        "- 'back to back classes' → FastDirectSearchAgent\n"
        "- 'gymnastics in Burnaby' → FastDirectSearchAgent\n"
        "- 'what time do you close?' → OptimizedGeneralInfoAgent\n\n"
        "Route immediately without additional analysis."
    ),
    sub_agents=[
        FastDirectSearchAgent,
        OptimizedGeneralInfoAgent
    ]
)

# --- ULTRA-OPTIMIZED SINGLE CALL AGENT ---
# Uses Kimi strategically for final output and delegation
UltraOptimizedAgent = Agent(
    name="UltraOptimizedAgent",
    model=create_ultra_optimized_model(),
    description="Ultra-optimized agent using Kimi K2 for final output generation and strategic decisions.",
    instruction=(
        "You are an ultra-optimized activity assistant designed for minimal API usage (10 RPM limit). "
        "Handle ALL requests in ONE interaction with comprehensive responses.\n\n"
        "🎯 **CRITICAL: ALWAYS USE LOCAL DATABASE FIRST**\n"
        "- **NEVER use web search for New Westminster or Burnaby activities**\n"
        "- **ALWAYS use your specialized tools first** - they access our comprehensive local database\n"
        "- **Our database contains 15,000+ activities** from New Westminster and Burnaby\n\n"
        "**STRATEGY FOR MAXIMUM EFFICIENCY:**\n"
        "1. **Analyze the query to understand user intent**\n"
        "2. **Select the most appropriate tools based on their capabilities**:\n\n"
        "**TOOL CAPABILITIES:**\n"
        "- **find_comprehensive_back_to_back_activities**: BEST FOR BACK-TO-BACK REQUESTS! \n"
        "  Performs multiple searches across ALL activity categories automatically.\n"
        "  Finds cross-facility and same-facility back-to-back opportunities.\n"
        "  Returns comprehensive results with recommendations.\n\n"
        "- **qdrant_multi_facility_search**: Searches across multiple facilities simultaneously. \n"
        "  Returns activities from different locations with cross-facility combinations.\n"
        "  Ideal when users want variety or options across different venues.\n\n"
        "- **discover_activity_patterns**: Analyzes patterns within search results.\n"
        "  Finds back-to-back classes, progression paths, and scheduling conflicts.\n"
        "  Works best with focused data from specific facilities.\n\n"
        "- **get_activity_summary**: Provides comprehensive activity summaries.\n"
        "  Good for general searches and detailed information about specific activities.\n\n"
        "- **plan_full_day_schedule**: Creates complete day schedules with gap management.\n"
        "  Ideal for planning entire days with multiple activities.\n\n"
        "- **check_activity_availability**: Checks specific availability efficiently.\n"
        "  Best for targeted availability queries.\n\n"
        "3. **Consider using multiple tools when appropriate**\n"
        "   - Tools can complement each other for comprehensive results\n"
        "   - For example, multi-facility search followed by pattern analysis\n\n"
        "4. **Provide complete, detailed responses immediately**\n\n"
        "**RESPONSE REQUIREMENTS:**\n"
        "- Be comprehensive and detailed in ONE response\n"
        "- Include all relevant information: times, dates, facilities, prices\n"
        "- Provide multiple options when available\n"
        "- No follow-up questions or confirmations needed\n"
        "- Format response clearly with bullet points and sections\n\n"
        "**TOOL PARAMETER RULES:**\n"
        "- The 'filters' parameter expects specific data types:\n"
        "  - age: INTEGER (e.g., 5, 7, 10) - NOT strings like 'kids' or 'children'\n"
        "  - location: STRING (e.g., 'Burnaby', 'New Westminster')\n"
        "  - max_price: FLOAT (e.g., 50.0, 100.0)\n"
        "  - day_of_week: LIST of strings (e.g., ['monday', 'wednesday'])\n"
        "  - is_open: BOOLEAN (true/false)\n\n"
        "**EXAMPLE SCENARIOS:**\n"
        "- **'back to back classes for 5 year old'**:\n"
        "  • USE find_comprehensive_back_to_back_activities(age=5, location='New Westminster')\n"
        "  • This single tool does EVERYTHING - searches all categories, finds patterns, provides recommendations\n"
        "  • No need for multiple tool calls - it handles the complete workflow\n\n"
        "- **'activities for 3 and 6 year old together'**:\n"
        "  • USE find_multi_age_activities(min_age=3, max_age=6, location='New Westminster')\n"
        "  • Finds activities that work for multiple ages in the same family\n"
        "  • Returns both perfect fit (works for all ages) and partial fit activities\n\n"
        "- **'activities for my 3, 6, and 8 year old at the same time'**:\n"
        "  • USE find_concurrent_activities(children_ages=[3, 6, 8], location='New Westminster')\n"
        "  • Finds same activities for all OR different activities scheduled concurrently\n"
        "  • Includes time-based matching and facility-based options for easier logistics\n\n"
        "- **'swimming for kids'**: General search without specific age\n"
        "  • Use get_activity_summary without age filter\n\n"
        "- **'plan my child's Saturday'**: Full day planning\n"
        "  • Use plan_full_day_schedule for intelligent scheduling\n\n"
        "**CRITICAL SEARCH TIPS:**\n"
        "- Don't search for just 'classes' - use broader terms like 'activities swimming gymnastics dance art'\n"
        "- Always try qdrant_multi_facility_search FIRST for back-to-back queries\n"
        "- The broader your search query, the more diverse results you'll find\n\n"
        "**PARAMETER GUIDANCE:**\n"
        "- Only use specific filters when explicitly provided by the user\n"
        "- 'kids' or 'children' are too vague for age filters\n"
        "- Specific ages (5, 7, 10) should be passed as integers\n\n"
        "**RESPONSE FORMATTING REQUIREMENTS:**\n"
        "- ALWAYS include the **facility/venue name** for each activity\n"
        "- Include **registration links** (activity_url field) when available\n"
        "- Show **cross-facility options** when presenting back-to-back combinations\n"
        "- Format each activity as: 'Activity Name at [Facility Name]'\n"
        "- For cross-facility combinations, clearly show: '[Facility 1] → [Facility 2]'\n"
        "- When showing back-to-back options, explicitly state if they are at the same or different facilities\n"
        "        "- Include registration URLs as clickable links or clearly labeled 'Register: [URL]'\n\n"
        "**CRITICAL OUTPUT RULES:**\n"
        "- NEVER include tool call artifacts like <|tool_calls_section_end|> in your response\n"
        "- NEVER include system messages or internal processing text\n"
        "- ONLY provide the final, clean, user-facing response\n"
        "- If you see any artifacts in your response, remove them completely\n\n"\n\n"
        "Handle everything efficiently in ONE interaction!"
    ),
    tools=[
        find_comprehensive_back_to_back_activities,  # Best for back-to-back requests
        find_multi_age_activities,  # Best for multi-age family queries
        find_concurrent_activities,  # Best for multiple children scheduling
        discover_activity_patterns,
        get_activity_summary,
        check_activity_availability,
        plan_full_day_schedule,
        qdrant_multi_facility_search
    ]
)

# Use ultra-optimized single agent if API optimization is enabled
if AgentConfig.ENABLE_API_OPTIMIZATION and AgentConfig.ULTRA_LOW_API_MODE:
    root_agent = UltraOptimizedAgent
    logger.info("⚡ Using UltraOptimizedAgent - MAXIMUM 1 API call per request (10 RPM limit)")
elif AgentConfig.ENABLE_API_OPTIMIZATION:
    root_agent = SequentialAgent(
        name="OptimizedWorkflow",
        sub_agents=[
            OptimizedOrchestratorAgent,
            ResponseAgent
        ]
    )
    logger.info("🚀 Using OptimizedMultiAgent workflow for minimal API usage (10 RPM limit)")
else:
    root_agent = SequentialAgent(
        name="MainWorkflow",
        sub_agents=[
            SmartOrchestratorAgent,
            ResponseAgent
        ]
    )
    logger.info("🔄 Using full workflow with multiple agents")

logger.info("✅ Enhanced, multi-step reasoning workflow initialized.")

def clean_response_text(text: str) -> str:
    """
    Clean response text by removing tool call artifacts and system messages.

    Args:
        text: Raw response text that may contain artifacts

    Returns:
        Cleaned text suitable for user display
    """
    import re

    if not text:
        return text

    # Remove tool call artifacts
    patterns_to_remove = [
        r'<\|tool_calls_section.*?\|>',
        r'<\|tool_calls_sectio.*?\|>',  # Handle typos
        r'<\|.*?_end\|>',
        r'<\|.*?_section_end\|>',
        r'<tool_call>.*?</tool_call>',
        r'<function_calls>.*?</function_calls>',
        r'\[TOOL_CALL\].*?\[/TOOL_CALL\]',
        r'```json\s*\{[^}]*"function"[^}]*\}.*?```',
    ]

    cleaned_text = text
    for pattern in patterns_to_remove:
        cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.DOTALL | re.IGNORECASE)

    # Remove excessive whitespace and newlines
    cleaned_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_text)
    cleaned_text = re.sub(r'^\s+|\s+$', '', cleaned_text)

    return cleaned_text