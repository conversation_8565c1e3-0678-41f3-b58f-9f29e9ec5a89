"""
Qdrant ADK Tools - Flexible query interface for the agent
Allows the agent to directly query Qdrant with custom parameters
"""

import logging
from typing import Dict, List, Optional, Any
from qdrant_client import AsyncQdrantClient, models
from google.adk.tools import ToolContext

from multi_tool_agent.config import AgentConfig

logger = logging.getLogger(__name__)

async def qdrant_flexible_search(
    collection_name: str,
    search_params: Dict[str, Any],
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Flexible Qdrant search tool that allows the agent to perform custom queries.
    
    Args:
        collection_name: Name of the Qdrant collection to search
        search_params: Dictionary containing search parameters:
            - query_text: Optional text to search for
            - filters: Optional dictionary of filter conditions
            - limit: Number of results to return (default: 20)
            - offset: Offset for pagination (default: 0)
            - with_vector: Whether to return vectors (default: False)
            - search_type: Type of search ("semantic", "filter", "scroll")
    
    Returns:
        Search results with metadata
    """
    try:
        qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
        
        # Extract parameters
        query_text = search_params.get("query_text", "")
        filters = search_params.get("filters", {})
        limit = search_params.get("limit", 20)
        offset = search_params.get("offset", 0)
        with_vector = search_params.get("with_vector", False)
        search_type = search_params.get("search_type", "semantic")
        
        logger.info(f"🔍 Qdrant flexible search: type={search_type}, query='{query_text}', filters={filters}")
        
        # Build Qdrant filter
        qdrant_filter = None
        if filters:
            must_conditions = []
            for key, value in filters.items():
                if isinstance(value, dict):
                    # Handle range filters
                    if "min" in value or "max" in value:
                        range_dict = {}
                        if "min" in value:
                            range_dict["gte"] = value["min"]
                        if "max" in value:
                            range_dict["lte"] = value["max"]
                        must_conditions.append(
                            models.FieldCondition(key=key, range=models.Range(**range_dict))
                        )
                    elif "contains" in value:
                        # Text match
                        must_conditions.append(
                            models.FieldCondition(key=key, match=models.MatchText(text=value["contains"]))
                        )
                else:
                    # Exact match
                    must_conditions.append(
                        models.FieldCondition(key=key, match=models.MatchValue(value=value))
                    )
            
            if must_conditions:
                qdrant_filter = models.Filter(must=must_conditions)
        
        # Perform search based on type
        results = []
        
        if search_type == "scroll" or (search_type == "filter" and not query_text):
            # Filter-only search using scroll
            response = await qdrant_client.scroll(
                collection_name=collection_name,
                scroll_filter=qdrant_filter,
                limit=limit,
                offset=offset,
                with_payload=True,
                with_vectors=with_vector
            )
            results = [{"payload": point.payload, "id": str(point.id)} for point in response[0]]
            
        else:
            # Semantic search (requires vector)
            # For now, use a simple hash-based vector as placeholder
            if query_text:
                text_vector = [hash(word) % 1000 / 1000.0 for word in query_text.split()]
                if len(text_vector) < 768:
                    text_vector.extend([0.0] * (768 - len(text_vector)))
                text_vector = text_vector[:768]
                
                response = await qdrant_client.search(
                    collection_name=collection_name,
                    query_vector=("dense", text_vector),
                    query_filter=qdrant_filter,
                    limit=limit,
                    offset=offset,
                    with_payload=True,
                    with_vectors=with_vector
                )
                results = [
                    {
                        "payload": point.payload,
                        "id": str(point.id),
                        "score": point.score
                    }
                    for point in response
                ]
        
        # Get collection info
        collection_info = await qdrant_client.get_collection(collection_name)
        
        return {
            "status": "success",
            "search_type": search_type,
            "total_results": len(results),
            "results": results,
            "collection_info": {
                "vectors_count": collection_info.vectors_count,
                "indexed_vectors_count": collection_info.indexed_vectors_count
            },
            "query_params": {
                "query_text": query_text,
                "filters": filters,
                "limit": limit,
                "offset": offset
            }
        }
        
    except Exception as e:
        logger.error(f"Error in qdrant_flexible_search: {e}", exc_info=True)
        return {
            "status": "error",
            "message": f"Qdrant search failed: {str(e)}",
            "search_type": search_type
        }
    finally:
        if 'qdrant_client' in locals():
            await qdrant_client.close()

async def qdrant_get_collection_schema(
    collection_name: str,
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Get the schema and field information for a Qdrant collection.
    This helps the agent understand what fields are available for filtering.
    
    Args:
        collection_name: Name of the collection to inspect
    
    Returns:
        Collection schema information
    """
    try:
        qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
        
        # Get collection info
        collection_info = await qdrant_client.get_collection(collection_name)
        
        # Sample some points to understand the schema
        sample_points = await qdrant_client.scroll(
            collection_name=collection_name,
            limit=5,
            with_payload=True
        )
        
        # Extract field names and types from sample
        field_info = {}
        if sample_points[0]:
            for point in sample_points[0]:
                if point.payload:
                    for key, value in point.payload.items():
                        if key not in field_info:
                            field_info[key] = {
                                "type": type(value).__name__,
                                "example": value if not isinstance(value, (list, dict)) else str(value)[:50]
                            }
        
        return {
            "status": "success",
            "collection_name": collection_name,
            "vectors_count": collection_info.vectors_count,
            "indexed_vectors_count": collection_info.indexed_vectors_count,
            "field_info": field_info,
            "available_fields": list(field_info.keys())
        }
        
    except Exception as e:
        logger.error(f"Error in qdrant_get_collection_schema: {e}", exc_info=True)
        return {
            "status": "error",
            "message": f"Failed to get collection schema: {str(e)}"
        }
    finally:
        if 'qdrant_client' in locals():
            await qdrant_client.close()

async def qdrant_multi_facility_search(
    activity_type: str,
    location: str,
    date: str,
    age: int,
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Specialized search for finding activities across multiple facilities.
    This addresses the limitation of single-facility results.
    
    Args:
        activity_type: Type of activity to search for
        location: Location/city to search in
        date: Date for activities (YYYY-MM-DD format)
        age: Age of the child
    
    Returns:
        Activities grouped by facility with cross-facility suggestions
    """
    try:
        qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
        
        # Build comprehensive filter
        must_conditions = []
        
        # Location filter
        if location:
            if "new west" in location.lower():
                must_conditions.append(
                    models.FieldCondition(key="city", match=models.MatchValue(value="New Westminster"))
                )
        
        # Date filter - dates are stored as strings in YYYY-MM-DD format
        if date:
            must_conditions.append(models.FieldCondition(key="start_date", match=models.MatchValue(value=date)))
            # For finding activities that are active on this date, we'd need to check if date falls between start/end
            # But since dates are stored as strings, we can't use range queries effectively
            # Instead, we'll get all activities and filter in post-processing
        
        # Age filter
        if age:
            must_conditions.append(models.FieldCondition(key="min_age_years", range=models.Range(lte=age)))
            must_conditions.append(models.FieldCondition(key="max_age_years", range=models.Range(gte=age)))
        
        qdrant_filter = models.Filter(must=must_conditions) if must_conditions else None
        
        # Search with larger limit to get variety
        response = await qdrant_client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=qdrant_filter,
            limit=100,  # Get more results for variety
            with_payload=True
        )
        
        # Group by facility
        facility_groups = {}
        for point in response[0]:
            facility = point.payload.get("facility", "Unknown")
            if facility not in facility_groups:
                facility_groups[facility] = []
            facility_groups[facility].append(point.payload)
        
        # Create cross-facility combinations
        cross_facility_suggestions = []
        facilities = list(facility_groups.keys())
        
        for i, facility1 in enumerate(facilities):
            for facility2 in facilities[i+1:]:
                # Pick sample activities from each facility
                if facility_groups[facility1] and facility_groups[facility2]:
                    suggestion = {
                        "combination": f"{facility1} + {facility2}",
                        "sample_activities": [
                            facility_groups[facility1][0],
                            facility_groups[facility2][0]
                        ],
                        "travel_consideration": "Allow 30-45 minutes between facilities"
                    }
                    cross_facility_suggestions.append(suggestion)
        
        return {
            "status": "success",
            "total_activities": len(response[0]),
            "facilities_found": len(facility_groups),
            "facility_breakdown": {
                facility: {
                    "count": len(activities),
                    "sample_activities": activities[:3]  # First 3 from each
                }
                for facility, activities in facility_groups.items()
            },
            "cross_facility_suggestions": cross_facility_suggestions[:5],  # Top 5 combinations
            "search_params": {
                "activity_type": activity_type,
                "location": location,
                "date": date,
                "age": age
            }
        }
        
    except Exception as e:
        logger.error(f"Error in qdrant_multi_facility_search: {e}", exc_info=True)
        return {
            "status": "error",
            "message": f"Multi-facility search failed: {str(e)}"
        }
    finally:
        if 'qdrant_client' in locals():
            await qdrant_client.close()
