#!/usr/bin/env python3
"""
Test the improved artifact cleaning to ensure tool call artifacts are removed.
"""

import asyncio
import json
import websockets
import time

async def test_artifact_cleaning():
    """Test that tool call artifacts are properly cleaned from responses."""
    
    print('🧪 Testing improved artifact cleaning...')
    print('=' * 60)
    
    # Test the cleaning functions directly first
    print('1. Testing cleaning functions directly...')
    
    try:
        import sys
        import os
        sys.path.append('.')
        import adk_server
        
        # Test cases with various artifact patterns
        test_cases = [
            {
                'input': 'Hello <|tool_calls_section_end|> world',
                'expected_clean': True,
                'description': 'Basic tool call artifact'
            },
            {
                'input': 'Test <|tool_calls_sectioall_end|> content <|tool_calls_section_end|>',
                'expected_clean': True,
                'description': 'Multiple artifacts with typos'
            },
            {
                'input': 'I\'ll find activities <|tool_calls_sectioall_end|> Perfect! Here are the results...',
                'expected_clean': True,
                'description': 'Real-world pattern with thinking text'
            },
            {
                'input': 'Normal response without any artifacts',
                'expected_clean': True,
                'description': 'Clean text should remain unchanged'
            }
        ]
        
        all_passed = True
        for i, test_case in enumerate(test_cases):
            cleaned = adk_server.final_response_post_processor(test_case['input'])
            has_artifacts = '<|' in cleaned
            
            if has_artifacts and test_case['expected_clean']:
                print(f'   ❌ Test {i+1} FAILED: {test_case["description"]}')
                print(f'      Input: {test_case["input"]}')
                print(f'      Output: {cleaned}')
                all_passed = False
            else:
                print(f'   ✅ Test {i+1} PASSED: {test_case["description"]}')
        
        if all_passed:
            print('✅ All direct cleaning tests passed!')
        else:
            print('❌ Some direct cleaning tests failed!')
            
    except Exception as e:
        print(f'❌ Error testing cleaning functions: {e}')
        return False
    
    # Test with actual WebSocket connection
    print('\n2. Testing with WebSocket connection...')
    
    try:
        # Wait a moment for server to be ready
        await asyncio.sleep(2)
        
        ws_url = "ws://localhost:8080/ws"
        
        async with websockets.connect(ws_url) as websocket:
            print('✅ WebSocket connected')
            
            # Send a query that might trigger artifacts
            message = {
                "type": "user_message",
                "content": "any activities that a 3 year old and a 6 year old can take together?",
                "session_id": f"artifact_test_{int(time.time())}"
            }
            
            await websocket.send(json.dumps(message))
            print('📤 Sent test query')
            
            # Collect the response
            response_text = ""
            response_complete = False
            
            print('📥 Receiving response...')
            
            while not response_complete:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                    data = json.loads(response)
                    
                    if data.get("type") == "text_chunk":
                        chunk = data.get("content", "")
                        response_text += chunk
                        print(".", end="", flush=True)
                    
                    elif data.get("type") == "turn_complete":
                        response_complete = True
                        print("\n✅ Response complete")
                        
                except asyncio.TimeoutError:
                    print("\n⏰ Response timeout")
                    break
                except Exception as e:
                    print(f"\n❌ Error receiving response: {e}")
                    break
            
            # Analyze the response
            if response_text:
                print('\n3. Analyzing response for artifacts...')
                
                # Check for tool call artifacts
                artifacts_found = []
                artifact_patterns = [
                    '<|tool_calls_section',
                    '<|tool_calls_sectio',
                    'tool_calls_section_end',
                    'tool_calls_sectioall_end'
                ]
                
                for pattern in artifact_patterns:
                    if pattern in response_text:
                        artifacts_found.append(pattern)
                
                if artifacts_found:
                    print(f'❌ ARTIFACTS STILL PRESENT: {artifacts_found}')
                    print(f'Response preview: {response_text[:200]}...')
                    return False
                else:
                    print('✅ NO ARTIFACTS FOUND in response!')
                    
                # Check response quality
                if len(response_text.strip()) > 100:
                    print('✅ Response has good length')
                else:
                    print('⚠️ Response seems short, might be over-cleaned')
                
                # Check for expected content
                expected_content = ['activities', 'year', 'old']
                missing_content = [word for word in expected_content if word.lower() not in response_text.lower()]
                
                if not missing_content:
                    print('✅ Response contains expected content')
                else:
                    print(f'⚠️ Missing expected content: {missing_content}')
                
                print(f'\n📊 Response length: {len(response_text)} characters')
                print('✅ ARTIFACT CLEANING TEST PASSED!')
                return True
            else:
                print('❌ No response received')
                return False
                
    except Exception as e:
        print(f'❌ WebSocket test error: {e}')
        return False

async def main():
    """Run the artifact cleaning test."""
    print('🚀 TESTING IMPROVED ARTIFACT CLEANING')
    print('Verifying that tool call artifacts are properly removed from responses')
    print()
    
    success = await test_artifact_cleaning()
    
    print('\n' + '=' * 60)
    if success:
        print('🎉 ARTIFACT CLEANING TEST PASSED!')
        print('✅ Tool call artifacts are being properly removed')
        print('✅ Ready to proceed with server optimization')
    else:
        print('❌ ARTIFACT CLEANING TEST FAILED!')
        print('⚠️ Tool call artifacts still need attention')
    
    return success

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
