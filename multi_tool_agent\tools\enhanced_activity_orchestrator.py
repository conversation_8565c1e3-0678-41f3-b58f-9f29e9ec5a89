"""
Enhanced Activity Orchestrator
Provides multi-turn search capabilities and comprehensive activity discovery
to overcome the single API call limitation.
"""

import logging
import async<PERSON>
from typing import Dict, List, Any, Optional
from datetime import datetime
from google.adk.tools import ToolContext

from .activity_search_tools import raw_activity_search, ActivityFilters
from .intelligent_activity_tools import discover_activity_patterns

logger = logging.getLogger(__name__)

class ActivityOrchestrator:
    """Orchestrates multiple searches to find comprehensive activity options."""
    
    @staticmethod
    async def comprehensive_back_to_back_search(
        age: Optional[int] = None,
        location: str = "New Westminster",
        date: Optional[str] = None,
        tool_context: Optional[ToolContext] = None
    ) -> Dict[str, Any]:
        """
        Performs comprehensive multi-turn search for back-to-back activities.
        This bypasses the single API call limitation by pre-computing multiple searches.
        
        Args:
            age: Age of the child
            location: Location/city to search in
            date: Optional date filter (YYYY-MM-DD)
            tool_context: Tool context for ADK
            
        Returns:
            Comprehensive results with back-to-back opportunities
        """
        try:
            # Define activity categories to search
            activity_categories = [
                "swimming", "gymnastics", "dance", "art", "sports",
                "music", "martial arts", "skating", "education", "stem"
            ]
            
            all_activities = []
            facility_map = {}
            
            # Search each category
            logger.info(f"🔍 Starting comprehensive search for {age} year old in {location}")
            
            for category in activity_categories:
                filters = {
                    "location": location
                }

                # Only add age filter if age is specified
                if age is not None:
                    filters["age"] = age

                if date:
                    filters["date"] = date
                
                # Search for activities in this category
                result = await raw_activity_search(
                    query=category,
                    filters=filters,
                    tool_context=tool_context,
                    find_back_to_back=False
                )
                
                if result.get("status") == "success" and result.get("results"):
                    activities = result["results"]
                    all_activities.extend(activities)
                    
                    # Group by facility
                    for activity in activities:
                        facility = activity.get("facility", "Unknown")
                        if facility not in facility_map:
                            facility_map[facility] = []
                        facility_map[facility].append(activity)
                    
                    logger.info(f"✅ Found {len(activities)} {category} activities")
            
            # Now discover back-to-back patterns
            if all_activities:
                pattern_filters = {"location": location}
                if age is not None:
                    pattern_filters["age"] = age

                pattern_result = await discover_activity_patterns(
                    query="all activities",
                    filters=pattern_filters,
                    pattern_types=["back_to_back"],
                    tool_context=tool_context
                )
                
                back_to_back_opportunities = []
                if pattern_result.get("patterns", {}).get("back_to_back", {}).get("opportunities"):
                    back_to_back_opportunities = pattern_result["patterns"]["back_to_back"]["opportunities"]
            
            # Analyze same-facility opportunities
            same_facility_opportunities = []
            for facility, activities in facility_map.items():
                if len(activities) >= 2:
                    # Find activities that can be taken on the same day
                    for i, act1 in enumerate(activities):
                        for act2 in activities[i+1:]:
                            if _can_be_back_to_back(act1, act2):
                                same_facility_opportunities.append({
                                    "facility": facility,
                                    "activities": [act1, act2],
                                    "benefit": "No travel time between activities"
                                })
            
            # Prepare comprehensive response
            return {
                "status": "success",
                "summary": {
                    "total_activities_found": len(all_activities),
                    "facilities_covered": len(facility_map),
                    "back_to_back_opportunities": len(back_to_back_opportunities),
                    "same_facility_opportunities": len(same_facility_opportunities)
                },
                "back_to_back_opportunities": back_to_back_opportunities[:10],
                "same_facility_opportunities": same_facility_opportunities[:5],
                "facility_breakdown": {
                    facility: {
                        "count": len(activities),
                        "categories": list(set(act.get("category", "Other") for act in activities)),
                        "sample_activities": activities[:3]
                    }
                    for facility, activities in list(facility_map.items())[:5]
                },
                "search_parameters": {
                    "age": age,
                    "location": location,
                    "date": date,
                    "categories_searched": activity_categories
                },
                "recommendations": _generate_recommendations(
                    all_activities, back_to_back_opportunities, facility_map
                )
            }
            
        except Exception as e:
            logger.error(f"Error in comprehensive search: {e}", exc_info=True)
            return {
                "status": "error",
                "message": f"Comprehensive search failed: {str(e)}"
            }


def _can_be_back_to_back(act1: Dict, act2: Dict) -> bool:
    """Check if two activities can be taken back-to-back."""
    # Check if they're on the same day
    days1 = set(act1.get("days_of_week_list", []))
    days2 = set(act2.get("days_of_week_list", []))
    
    if not days1.intersection(days2):
        return False
    
    # Check if times don't conflict
    start1 = act1.get("start_time_iso")
    end1 = act1.get("end_time_iso")
    start2 = act2.get("start_time_iso")
    end2 = act2.get("end_time_iso")
    
    if not all([start1, end1, start2, end2]):
        return False
    
    # Simple time comparison (would need proper datetime parsing in production)
    return end1 < start2 or end2 < start1


def _generate_recommendations(
    activities: List[Dict],
    back_to_back: List[Dict],
    facility_map: Dict[str, List[Dict]]
) -> List[str]:
    """Generate smart recommendations based on the search results."""
    recommendations = []
    
    if not activities:
        recommendations.append("No activities found. Try expanding your search criteria.")
        return recommendations
    
    if back_to_back:
        recommendations.append(
            f"Found {len(back_to_back)} back-to-back opportunities! "
            "These allow your child to attend multiple activities in one trip."
        )
    else:
        recommendations.append(
            "No direct back-to-back opportunities found. Consider:"
        )
        recommendations.append(
            "• Looking at activities on different days"
        )
        recommendations.append(
            "• Checking nearby facilities for complementary schedules"
        )
    
    # Facility-specific recommendations
    multi_activity_facilities = [
        f for f, acts in facility_map.items() if len(acts) >= 3
    ]
    if multi_activity_facilities:
        recommendations.append(
            f"Consider {multi_activity_facilities[0]} which offers multiple "
            "activities - easier for scheduling and transportation."
        )
    
    return recommendations


# Export the orchestrator function
async def find_comprehensive_back_to_back_activities(
    age: Optional[int] = None,
    location: str = "New Westminster",
    date: Optional[str] = None,
    tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """
    Main entry point for comprehensive back-to-back activity search.

    This tool performs multiple searches across different activity categories
    to find all possible back-to-back opportunities for a child.

    Args:
        age: Optional age of the child (e.g., 5 for a 5-year-old). If None, shows all ages.
        location: City or area to search in (e.g., "New Westminster")
        date: Optional specific date in YYYY-MM-DD format
        tool_context: ADK tool context

    Returns:
        Comprehensive results including:
        - Back-to-back opportunities across all categories
        - Same-facility options for convenience
        - Facility breakdown with activity counts
        - Smart recommendations
    """
    orchestrator = ActivityOrchestrator()
    return await orchestrator.comprehensive_back_to_back_search(
        age=age,
        location=location,
        date=date,
        tool_context=tool_context
    )
