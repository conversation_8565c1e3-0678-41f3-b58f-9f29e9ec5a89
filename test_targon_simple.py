"""
Simple test for Targon API client without full module dependencies
"""

import asyncio
import os
import sys
import logging
import aiohttp
import json
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class SimpleTargonClient:
    """Simple Targon client for testing."""
    
    def __init__(self, api_key: str, api_base: str = "https://api.targon.com/v1"):
        self.api_key = api_key
        self.api_base = api_base.rstrip('/')
        
    async def stream_completion(self, 
                              model: str,
                              messages: list,
                              temperature: float = 0.7,
                              max_tokens: int = 4096):
        """Stream completion from Targon API."""
        
        url = f"{self.api_base}/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": True
        }
        
        logger.info(f"🔗 Connecting to {url}")
        logger.info(f"🔑 Using API key: {self.api_key[:10]}...")
        logger.info(f"📋 Model: {model}")
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(url, headers=headers, json=payload) as response:
                    logger.info(f"📡 Response status: {response.status}")
                    
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"❌ API Error: {response.status} - {error_text}")
                        raise Exception(f"Targon API error: {response.status} - {error_text}")
                    
                    response_chunks = []
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        if line.startswith('data: '):
                            data_str = line[6:]  # Remove 'data: ' prefix
                            if data_str == '[DONE]':
                                break
                            
                            try:
                                data = json.loads(data_str)
                                if 'choices' in data and data['choices']:
                                    choice = data['choices'][0]
                                    if 'delta' in choice and 'content' in choice['delta']:
                                        content = choice['delta']['content']
                                        if content:
                                            response_chunks.append(content)
                                            yield content
                            except json.JSONDecodeError:
                                logger.warning(f"Failed to parse SSE data: {data_str}")
                                continue
                    
                    logger.info(f"✅ Received {len(response_chunks)} chunks")
                    
            except aiohttp.ClientError as e:
                logger.error(f"❌ Connection error: {e}")
                raise
            except Exception as e:
                logger.error(f"❌ Unexpected error: {e}")
                raise

async def test_targon_api():
    """Test direct Targon API connection."""
    logger.info("🚀 Testing Targon API connection...")
    
    # Get configuration
    api_key = os.getenv('TARGON_API_KEY')
    api_base = os.getenv('TARGON_API_BASE', 'https://api.targon.com/v1')
    model = os.getenv('TARGON_KIMI_MODEL', 'moonshotai/Kimi-K2-Instruct')
    
    if not api_key:
        logger.error("❌ TARGON_API_KEY not found in environment")
        return False
    
    logger.info(f"📋 API Base: {api_base}")
    logger.info(f"📋 Model: {model}")
    logger.info(f"🔑 API Key: {api_key[:10]}...")
    
    # Create client
    client = SimpleTargonClient(api_key, api_base)
    
    # Test message
    messages = [{"role": "user", "content": "Hello! Please respond with 'Hi there!' to confirm you're working."}]
    
    try:
        logger.info("📡 Sending test message...")
        response_text = ""
        
        async for chunk in client.stream_completion(model, messages):
            response_text += chunk
            print(chunk, end="", flush=True)
        
        print()  # New line after streaming
        logger.info(f"✅ Full response: {response_text}")
        logger.info(f"✅ Response length: {len(response_text)} characters")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the test."""
    logger.info("🚀 Starting simple Targon API test...")
    
    success = await test_targon_api()
    
    if success:
        logger.info("🎉 Test passed! Targon API is working correctly.")
    else:
        logger.error("⚠️ Test failed. Please check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
