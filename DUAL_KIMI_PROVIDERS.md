# Dual Kimi Provider System

## Overview

The system now uses both Novita and Targon as Kimi K2 providers, effectively doubling the available rate limit from 10 RPM to 20 RPM (10 RPM each).

## Fallback Chain

```
User Request
    ↓
1. Try Novita Kimi K2 (10 RPM limit)
    ↓ If rate limited (429 error)
2. Try Targon Kimi K2 (10 RPM limit)
    ↓ If also rate limited
3. Use unlimited Gemini models
```

## How It Works

### Request Flow

1. **Initial Request**: System tries Novita Kimi first
2. **Novita Rate Limited**: Automatically tries Targon Kimi
3. **Both Rate Limited**: Falls back to unlimited Gemini
4. **Smart Recovery**: After cooldown, returns to trying Kimi providers

### Rate Limiter Logic

Each provider has its own rate limiter:
- **Novita**: 10 RPM, 6.5 second minimum interval
- **Targon**: 10 RPM, 6.5 second minimum interval
- **Combined**: Up to 20 RPM when alternating between providers

### Configuration

The system automatically detects Targon availability by checking for `TARGON_API_KEY` in the environment.

## Monitoring

The API stats endpoint (`/api-stats`) tracks usage for both providers:

```json
{
  "current_novita_kimi_rpm": 8,
  "current_targon_kimi_rpm": 7,
  "total_kimi_rpm": 15,
  "calls_by_model": {
    "novita_kimi": 145,
    "targon_kimi": 132,
    "gemini-2.5-flash": 289
  }
}
```

## Expected Behavior

### Normal Operation (Under 10 RPM)
- All requests go to Novita Kimi
- Targon remains idle as backup
- Gemini used for non-strategic tasks

### Medium Load (10-20 RPM)
- Novita handles first 10 RPM
- Targon handles overflow up to 10 RPM
- Gemini still used for non-strategic tasks

### High Load (Over 20 RPM)
- Both Kimi providers at capacity
- All requests fall back to Gemini
- System automatically recovers when load decreases

## Benefits

1. **Double Capacity**: 20 RPM combined for Kimi requests
2. **Automatic Failover**: Seamless switching between providers
3. **No Code Changes**: Works transparently with existing agents
4. **Cost Optimization**: Still uses Gemini for majority of tasks
5. **Better Reliability**: Redundancy if one provider has issues

## Logs to Watch

```
🧠 Using Kimi K2 for final_output           # Novita being used
🔄 Using Targon Kimi K2 as fallback        # Targon being used
⚡ Using unlimited Gemini for final_output  # Both rate limited
🚫 Novita Kimi rate limited                 # Novita hit limit
🚫 Targon Kimi also rate limited           # Targon hit limit
```

## Testing

To test the dual provider system:

1. **Generate rapid requests** to trigger rate limits
2. **Watch the logs** to see provider switching
3. **Check `/api-stats`** to verify both providers are being used
4. **Monitor recovery** after rate limit cooldown

## Troubleshooting

### Targon Not Being Used
- Check `TARGON_API_KEY` is set in `.env`
- Verify Targon API is accessible
- Check logs for Targon initialization

### Both Providers Rate Limited Too Often
- Reduce Kimi usage by marking fewer tasks as strategic
- Increase use of Gemini for non-critical tasks
- Consider adding more providers

### Uneven Distribution
- This is normal - Novita is always tried first
- Targon only activates when Novita is rate limited
- Even distribution only occurs at sustained high load
