#!/usr/bin/env python3
"""
Debug data structure in Qdrant
"""

import os
import logging
import json
from dotenv import load_dotenv
from qdrant_client import QdrantClient, models

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_data_structure():
    """Debug the data structure in Qdrant."""
    
    # Get configuration from environment
    QDRANT_URL = os.environ.get("QDRANT_URL")
    QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")
    COLLECTION_NAME = "community_activities_v2"
    
    try:
        client = QdrantClient(
            url=QDRANT_URL,
            api_key=QDRANT_API_KEY
        )
        
        # Get a few points to examine structure
        logger.info("🔍 Examining data structure...")
        
        search_response = client.query_points(
            collection_name=COLLECTION_NAME,
            query=models.Document(
                text="swimming",
                model="BAAI/bge-base-en-v1.5"
            ),
            using="dense",
            limit=2,
            with_payload=True
        )
        
        results = search_response.points
        logger.info(f"Found {len(results)} results")
        
        for i, point in enumerate(results):
            logger.info(f"\n--- Point {i+1} ---")
            logger.info(f"ID: {point.id}")
            logger.info(f"Score: {point.score}")
            logger.info("Payload structure:")
            
            # Print the full payload structure
            payload = point.payload
            print(json.dumps(payload, indent=2, default=str))
            
            # Try different ways to access the name
            logger.info("\nTrying different name access methods:")
            logger.info(f"payload.get('name'): {payload.get('name')}")
            logger.info(f"payload.get('metadata', {{}}).get('name'): {payload.get('metadata', {}).get('name')}")
            
            # Show all top-level keys
            logger.info(f"Top-level keys: {list(payload.keys())}")
            
            if 'metadata' in payload:
                metadata = payload['metadata']
                logger.info(f"Metadata keys: {list(metadata.keys()) if isinstance(metadata, dict) else 'Not a dict'}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_data_structure()
