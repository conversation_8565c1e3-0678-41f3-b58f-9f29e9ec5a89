#!/usr/bin/env python3
"""
Test the RateLimitedLiteLlm model directly to ensure it's working.
"""

import asyncio
import logging
from multi_tool_agent.models.rate_limited_llm import create_ultra_optimized_model, RateLimitedLiteLlm
from multi_tool_agent.config import AgentConfig
from google.genai.types import Content, Part

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockContext:
    """Mock context for testing."""
    pass

async def test_rate_limited_model():
    """Test the rate limited model directly."""
    logger.info("🧪 Testing RateLimitedLiteLlm...")
    
    # Create the model
    model = create_ultra_optimized_model()
    logger.info(f"✅ Created model: {type(model).__name__}")
    
    # Create a mock request
    request = Content(
        role="user",
        parts=[Part(text="What swimming classes are available for a 5 year old?")]
    )
    
    # Create mock context
    ctx = MockContext()
    
    # Test that the model accepts the 'stream' parameter
    logger.info("📤 Testing generate_content_async with stream parameter...")
    
    try:
        # This is what ADK calls - with stream parameter
        responses = []
        async for response in model.generate_content_async(request, ctx, stream=True):
            responses.append(response)
            logger.info(f"✅ Received response chunk")
            
        logger.info(f"✅ Successfully received {len(responses)} response chunks")
        
    except TypeError as e:
        if "stream" in str(e):
            logger.error(f"❌ Still has parameter issue: {e}")
            return False
        else:
            logger.error(f"❌ Other TypeError: {e}")
            return False
    except Exception as e:
        logger.error(f"❌ Error during generation: {e}")
        # This might be expected if we're not connected to the actual API
        # But the important thing is that it accepted the 'stream' parameter
        if "API" in str(e) or "connection" in str(e).lower() or "request" in str(e).lower():
            logger.info("✅ Model accepted parameters correctly (API connection error is expected in test)")
            return True
        return False
    
    return True

async def test_parameter_handling():
    """Test that all ADK parameters are handled correctly."""
    logger.info("\n🧪 Testing parameter handling...")
    
    model = RateLimitedLiteLlm(
        model=AgentConfig.KIMI_MODEL,
        task_type="test",
        is_critical=True
    )
    
    # Test the method signature
    import inspect
    sig = inspect.signature(model.generate_content_async)
    logger.info(f"📋 Method signature: {sig}")
    
    # Check that it accepts kwargs
    params = list(sig.parameters.keys())
    if "kwargs" in params or any(p for p in sig.parameters.values() if p.kind == inspect.Parameter.VAR_KEYWORD):
        logger.info("✅ Method accepts **kwargs for additional parameters")
        return True
    else:
        logger.error("❌ Method does not accept **kwargs")
        return False

async def main():
    """Run all tests."""
    logger.info("🚀 Starting RateLimitedLiteLlm tests...")
    
    all_passed = True
    
    # Test 1: Direct model test
    if not await test_rate_limited_model():
        all_passed = False
        
    # Test 2: Parameter handling
    if not await test_parameter_handling():
        all_passed = False
    
    if all_passed:
        logger.info("\n✅ All tests passed! The fix is working.")
    else:
        logger.error("\n❌ Some tests failed. Please check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    asyncio.run(main())
