"""
Standalone test for Targon integration without package dependencies
"""

import asyncio
import os
import sys
import logging
import time
from dotenv import load_dotenv

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Import specific modules directly
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'multi_tool_agent'))

async def test_config():
    """Test configuration loading."""
    try:
        # Import config directly
        from multi_tool_agent.config import AgentConfig
        
        logger.info("✅ Successfully imported AgentConfig")
        logger.info(f"✅ Targon API Key: {AgentConfig.TARGON_API_KEY[:10]}...")
        logger.info(f"✅ Targon Model: {AgentConfig.TARGON_KIMI_MODEL}")
        logger.info(f"✅ Targon API Base: {AgentConfig.TARGON_API_BASE}")
        logger.info(f"✅ Use Targon Fallback: {AgentConfig.USE_TARGON_FALLBACK}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_targon_client():
    """Test Targon client directly."""
    try:
        # Import Targon client directly
        from multi_tool_agent.utils.targon_client import TargonClient
        
        logger.info("✅ Successfully imported TargonClient")
        
        # Create client
        api_key = os.getenv('TARGON_API_KEY')
        api_base = os.getenv('TARGON_API_BASE', 'https://api.targon.com/v1')
        
        client = TargonClient(api_key, api_base)
        logger.info("✅ Created Targon client")
        
        # Test streaming
        messages = [{"role": "user", "content": "Say 'Direct client test successful!' to confirm."}]
        
        response_text = ""
        async for chunk in client.stream_completion(
            model="moonshotai/Kimi-K2-Instruct",
            messages=messages,
            temperature=0.7
        ):
            response_text += chunk
            print(chunk, end="", flush=True)
        
        print()  # New line
        logger.info(f"✅ Response: {response_text}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Targon client test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_rate_limiter():
    """Test rate limiter directly."""
    try:
        # Import rate limiter components directly
        from multi_tool_agent.utils.rate_limiter import RateLimitConfig, KimiRateLimiter
        
        logger.info("✅ Successfully imported rate limiter components")
        
        # Create rate limiter
        config = RateLimitConfig(
            rpm=2,
            min_interval=1.0,
            max_queue_size=10,
            queue_timeout=5.0,
            enable_fallback=True
        )
        
        limiter = KimiRateLimiter(config)
        logger.info("✅ Created rate limiter")
        
        # Test acquisition
        start_time = time.time()
        async with limiter.acquire():
            logger.info("✅ First request acquired")
            await asyncio.sleep(0.1)
        
        async with limiter.acquire():
            elapsed = time.time() - start_time
            logger.info(f"✅ Second request acquired after {elapsed:.2f}s")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Rate limiter test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_hybrid_manager():
    """Test hybrid manager."""
    try:
        # Import hybrid manager directly
        from multi_tool_agent.utils.rate_limiter import HybridModelManager
        
        logger.info("✅ Successfully imported HybridModelManager")
        
        # Create manager
        manager = HybridModelManager()
        logger.info("✅ Created hybrid model manager")
        
        # Test configuration
        logger.info(f"✅ DeepInfra enabled: {manager.use_deepinfra}")
        logger.info(f"✅ DeepInfra model: {manager.deepinfra_kimi_model}")
        logger.info(f"✅ DeepInfra API base: {manager.deepinfra_api_base}")
        
        # Test model selection
        model = manager.get_model_for_task("reasoning", is_critical=True)
        logger.info(f"✅ Selected model for reasoning: {model}")
        
        # Test should_use_kimi
        should_use = await manager.should_use_kimi("reasoning", is_critical=True)
        logger.info(f"✅ Should use Kimi for reasoning: {should_use}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Hybrid manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_full_integration():
    """Test full integration."""
    try:
        # Import components
        from multi_tool_agent.utils.targon_client import get_targon_client
        from multi_tool_agent.utils.rate_limiter import get_hybrid_manager
        
        logger.info("✅ Successfully imported integration components")
        
        # Get components
        client = get_targon_client()
        manager = get_hybrid_manager()
        
        logger.info("✅ Got client and manager")
        
        # Test with rate limiter
        if manager.use_deepinfra:
            async with manager.deepinfra_limiter.acquire():
                logger.info("✅ Acquired Targon rate limiter")
                
                # Test streaming with rate limiter
                messages = [{"role": "user", "content": "Say 'Full integration test successful!' to confirm."}]
                
                response_text = ""
                async for chunk in client.stream_completion(
                    model=manager.targon_kimi_model,
                    messages=messages,
                    temperature=0.7
                ):
                    response_text += chunk
                    print(chunk, end="", flush=True)
                
                print()  # New line
                logger.info(f"✅ Response: {response_text}")
                
                return True
        else:
            logger.warning("⚠️ Targon not enabled")
            return False
        
    except Exception as e:
        logger.error(f"❌ Full integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    logger.info("🚀 Starting standalone Targon integration tests...")
    
    # Test 1: Config
    logger.info("\n=== Test 1: Configuration ===")
    success1 = await test_config()
    
    if not success1:
        logger.error("❌ Config test failed, stopping")
        return
    
    # Test 2: Targon client
    logger.info("\n=== Test 2: Targon Client ===")
    success2 = await test_targon_client()
    
    # Test 3: Rate limiter
    logger.info("\n=== Test 3: Rate Limiter ===")
    success3 = await test_rate_limiter()
    
    # Test 4: Hybrid manager
    logger.info("\n=== Test 4: Hybrid Manager ===")
    success4 = await test_hybrid_manager()
    
    # Test 5: Full integration
    logger.info("\n=== Test 5: Full Integration ===")
    success5 = await test_full_integration()
    
    # Summary
    logger.info("\n=== Test Summary ===")
    logger.info(f"Configuration: {'✅ PASSED' if success1 else '❌ FAILED'}")
    logger.info(f"Targon Client: {'✅ PASSED' if success2 else '❌ FAILED'}")
    logger.info(f"Rate Limiter: {'✅ PASSED' if success3 else '❌ FAILED'}")
    logger.info(f"Hybrid Manager: {'✅ PASSED' if success4 else '❌ FAILED'}")
    logger.info(f"Full Integration: {'✅ PASSED' if success5 else '❌ FAILED'}")
    
    if success1 and success2 and success3 and success4 and success5:
        logger.info("🎉 All tests passed! Targon integration is working correctly.")
    else:
        logger.error("⚠️ Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
