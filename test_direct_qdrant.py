"""
Direct test of Qdrant data - bypasses module imports
Tests real data directly from Qdrant
"""

import asyncio
import logging
from qdrant_client import AsyncQdrantClient, models
import os
from datetime import datetime
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Get Qdrant credentials directly
QDRANT_URL = os.getenv("QDRANT_URL", "https://7193c1cf-a88e-4252-8b1b-ab480cdcd7d0.us-west-2-0.aws.cloud.qdrant.io:6333")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY", "")
QDRANT_COLLECTION_NAME = "community_activities_v2"

async def test_real_qdrant_directly():
    """Test Qdrant directly without any module imports."""
    logger.info("=" * 60)
    logger.info("🔍 DIRECT QDRANT TEST - REAL DATA")
    logger.info("=" * 60)
    
    if not QDRANT_API_KEY:
        logger.error("❌ QDRANT_API_KEY not found in environment")
        return None
    
    logger.info(f"Connecting to: {QDRANT_URL}")
    logger.info(f"Collection: {QDRANT_COLLECTION_NAME}")
    
    # Create client
    client = AsyncQdrantClient(
        url=QDRANT_URL,
        api_key=QDRANT_API_KEY
    )
    
    try:
        # Check collection
        collection_info = await client.get_collection(QDRANT_COLLECTION_NAME)
        logger.info(f"✅ Collection exists with {collection_info.vectors_count} vectors")
        
        # Search for 5-year-olds in New Westminster
        logger.info("\n🔍 Searching for 5-year-olds in New Westminster...")
        
        must_conditions = [
            models.FieldCondition(key="min_age_years", range=models.Range(lte=5)),
            models.FieldCondition(key="max_age_years", range=models.Range(gte=5)),
            models.FieldCondition(key="city", match=models.MatchValue(value="New Westminster"))
        ]
        
        qdrant_filter = models.Filter(must=must_conditions)
        
        response = await client.scroll(
            collection_name=QDRANT_COLLECTION_NAME,
            scroll_filter=qdrant_filter,
            limit=200,
            with_payload=True
        )
        
        activities = [point.payload for point in response[0]]
        logger.info(f"✅ Found {len(activities)} activities for 5-year-olds in New Westminster")
        
        # Group by facility
        facilities = {}
        for activity in activities:
            facility = activity.get("facility", "Unknown")
            if facility not in facilities:
                facilities[facility] = []
            facilities[facility].append(activity)
        
        logger.info(f"\n🏢 Activities by facility:")
        for facility, acts in facilities.items():
            logger.info(f"   - {facility}: {len(acts)} activities")
        
        # Show sample activities
        logger.info("\n📋 Sample activities (first 5):")
        for i, activity in enumerate(activities[:5], 1):
            logger.info(f"\n{i}. {activity.get('name', 'Unknown')}")
            logger.info(f"   Facility: {activity.get('facility', 'Unknown')}")
            logger.info(f"   Category: {activity.get('category', 'Unknown')}")
            logger.info(f"   Time: {activity.get('start_time_iso', '')} - {activity.get('end_time_iso', '')}")
            logger.info(f"   Days: {activity.get('days_of_week_list', [])}")
            logger.info(f"   Dates: {activity.get('start_date', '')} to {activity.get('end_date', '')}")
            logger.info(f"   Price: ${activity.get('price_numeric', 'N/A')}")
        
        await client.close()
        return activities
        
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        await client.close()
        return None

def find_back_to_back_opportunities(activities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Find back-to-back opportunities in the activities."""
    opportunities = []
    
    # Group by date and facility
    by_date_facility = {}
    for activity in activities:
        if not all([
            activity.get('start_time_iso'),
            activity.get('end_time_iso'),
            activity.get('start_date'),
            activity.get('facility')
        ]):
            continue
            
        key = f"{activity['start_date']}|{activity['facility']}"
        if key not in by_date_facility:
            by_date_facility[key] = []
        by_date_facility[key].append(activity)
    
    # Find back-to-back in same facility
    for key, acts in by_date_facility.items():
        if len(acts) < 2:
            continue
            
        # Sort by start time
        sorted_acts = sorted(acts, key=lambda x: x.get('start_time_iso', ''))
        
        for i in range(len(sorted_acts) - 1):
            act1 = sorted_acts[i]
            act2 = sorted_acts[i + 1]
            
            # Calculate gap
            try:
                end1 = datetime.strptime(act1['end_time_iso'], '%H:%M:%S')
                start2 = datetime.strptime(act2['start_time_iso'], '%H:%M:%S')
                gap_minutes = (start2 - end1).seconds / 60
                
                if 5 <= gap_minutes <= 180:  # 5 minutes to 3 hours
                    opportunities.append({
                        'date': act1['start_date'],
                        'facility': act1['facility'],
                        'gap_minutes': int(gap_minutes),
                        'first': act1,
                        'second': act2
                    })
            except:
                continue
    
    return opportunities

async def main():
    """Run the direct test."""
    logger.info("🚀 Direct Qdrant Test - Real Data Only\n")
    
    # Get real activities
    activities = await test_real_qdrant_directly()
    
    if activities:
        # Find back-to-back opportunities
        logger.info("\n" + "=" * 60)
        logger.info("🔍 ANALYZING BACK-TO-BACK OPPORTUNITIES")
        logger.info("=" * 60)
        
        opportunities = find_back_to_back_opportunities(activities)
        logger.info(f"\n✅ Found {len(opportunities)} back-to-back opportunities")
        
        if opportunities:
            logger.info("\n📍 Sample back-to-back opportunities:")
            for i, opp in enumerate(opportunities[:5], 1):
                logger.info(f"\n--- Option {i} ---")
                logger.info(f"Date: {opp['date']}")
                logger.info(f"Facility: {opp['facility']}")
                logger.info(f"Gap: {opp['gap_minutes']} minutes")
                logger.info(f"First: {opp['first']['name']} ({opp['first']['start_time_iso']} - {opp['first']['end_time_iso']})")
                logger.info(f"Second: {opp['second']['name']} ({opp['second']['start_time_iso']} - {opp['second']['end_time_iso']})")
    
    logger.info("\n✅ This test used REAL data directly from Qdrant")
    logger.info("📊 No simulations - these are actual activities")

if __name__ == "__main__":
    asyncio.run(main())
