"""
Test script for enhanced back-to-back activity search
Tests the new comprehensive search functionality
"""

import asyncio
import logging
from typing import Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_comprehensive_search():
    """Test the comprehensive back-to-back search functionality."""
    try:
        # Import the enhanced orchestrator
        from multi_tool_agent.tools.enhanced_activity_orchestrator import find_comprehensive_back_to_back_activities
        
        logger.info("=" * 60)
        logger.info("Testing Enhanced Back-to-Back Search")
        logger.info("=" * 60)
        
        # Test 1: Search for 5-year-old in New Westminster
        logger.info("\n🧪 Test 1: Back-to-back classes for 5-year-old in New Westminster")
        result = await find_comprehensive_back_to_back_activities(
            age=5,
            location="New Westminster",
            tool_context=None
        )
        
        if result.get("status") == "success":
            summary = result.get("summary", {})
            logger.info(f"✅ Search completed successfully!")
            logger.info(f"   - Total activities found: {summary.get('total_activities_found', 0)}")
            logger.info(f"   - Facilities covered: {summary.get('facilities_covered', 0)}")
            logger.info(f"   - Back-to-back opportunities: {summary.get('back_to_back_opportunities', 0)}")
            logger.info(f"   - Same-facility opportunities: {summary.get('same_facility_opportunities', 0)}")
            
            # Show some examples if found
            if result.get("back_to_back_opportunities"):
                logger.info("\n📍 Sample back-to-back opportunities:")
                for i, opp in enumerate(result["back_to_back_opportunities"][:3], 1):
                    logger.info(f"\n   Option {i}:")
                    logger.info(f"   - Facilities: {opp.get('facilities', 'Unknown')}")
                    logger.info(f"   - Gap: {opp.get('gap_minutes', 0)} minutes ({opp.get('gap_type', '')})")
                    logger.info(f"   - First: {opp['first_class']['name']} at {opp['first_class']['facility']}")
                    logger.info(f"   - Second: {opp['second_class']['name']} at {opp['second_class']['facility']}")
        else:
            logger.error(f"❌ Search failed: {result.get('message', 'Unknown error')}")
        
        # Test 2: Search for 7-year-old in Burnaby
        logger.info("\n" + "=" * 60)
        logger.info("\n🧪 Test 2: Back-to-back classes for 7-year-old in Burnaby")
        result2 = await find_comprehensive_back_to_back_activities(
            age=7,
            location="Burnaby",
            tool_context=None
        )
        
        if result2.get("status") == "success":
            summary2 = result2.get("summary", {})
            logger.info(f"✅ Search completed successfully!")
            logger.info(f"   - Total activities found: {summary2.get('total_activities_found', 0)}")
            logger.info(f"   - Back-to-back opportunities: {summary2.get('back_to_back_opportunities', 0)}")
            
            # Show facility breakdown
            if result2.get("facility_breakdown"):
                logger.info("\n🏢 Top facilities with multiple activities:")
                for facility, data in list(result2["facility_breakdown"].items())[:3]:
                    logger.info(f"   - {facility}: {data['count']} activities")
                    logger.info(f"     Categories: {', '.join(data['categories'])}")
        
        return result, result2
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}", exc_info=True)
        return None, None

async def test_pattern_discovery():
    """Test the pattern discovery functionality directly."""
    try:
        from multi_tool_agent.tools.intelligent_activity_tools import discover_activity_patterns
        
        logger.info("\n" + "=" * 60)
        logger.info("Testing Pattern Discovery Tool")
        logger.info("=" * 60)
        
        # Test pattern discovery
        result = await discover_activity_patterns(
            query="activities swimming gymnastics dance art sports",
            filters={"min_age": 5, "max_age": 5, "location": "New Westminster"},
            pattern_types=["back_to_back"],
            tool_context=None
        )
        
        if result.get("status") == "success":
            logger.info("✅ Pattern discovery successful!")
            patterns = result.get("patterns", {})
            if patterns.get("back_to_back"):
                back_to_back = patterns["back_to_back"]
                logger.info(f"   - Found {back_to_back.get('total_opportunities', 0)} back-to-back patterns")
                logger.info(f"   - Summary: {back_to_back.get('summary', '')}")
        else:
            logger.error(f"❌ Pattern discovery failed: {result.get('message', 'Unknown error')}")
            
        return result
        
    except Exception as e:
        logger.error(f"❌ Pattern test failed: {e}", exc_info=True)
        return None

async def test_multi_facility_search():
    """Test the multi-facility search functionality."""
    try:
        from multi_tool_agent.tools.qdrant_adk_tools import qdrant_multi_facility_search
        
        logger.info("\n" + "=" * 60)
        logger.info("Testing Multi-Facility Search")
        logger.info("=" * 60)
        
        # Test multi-facility search
        result = await qdrant_multi_facility_search(
            activity_type="activities",
            location="New Westminster", 
            date=None,  # Don't filter by date for now
            age=5,
            tool_context=None
        )
        
        if result.get("status") == "success":
            logger.info("✅ Multi-facility search successful!")
            logger.info(f"   - Total activities: {result.get('total_activities', 0)}")
            logger.info(f"   - Facilities found: {result.get('facilities_found', 0)}")
            
            # Show cross-facility suggestions
            suggestions = result.get("cross_facility_suggestions", [])
            if suggestions:
                logger.info(f"\n🔄 Cross-facility suggestions: {len(suggestions)}")
                for i, sugg in enumerate(suggestions[:2], 1):
                    logger.info(f"   {i}. {sugg['combination']}")
        else:
            logger.error(f"❌ Multi-facility search failed: {result.get('message', 'Unknown error')}")
            
        return result
        
    except Exception as e:
        logger.error(f"❌ Multi-facility test failed: {e}", exc_info=True)
        return None

async def main():
    """Run all tests."""
    logger.info("🚀 Starting Enhanced Search Tests")
    logger.info("Testing the new comprehensive back-to-back search functionality\n")
    
    # Run tests
    comp_result1, comp_result2 = await test_comprehensive_search()
    pattern_result = await test_pattern_discovery()
    multi_result = await test_multi_facility_search()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 Test Summary")
    logger.info("=" * 60)
    
    tests_passed = 0
    tests_total = 4
    
    if comp_result1 and comp_result1.get("status") == "success":
        tests_passed += 1
        logger.info("✅ Comprehensive search (New West, age 5): PASSED")
    else:
        logger.info("❌ Comprehensive search (New West, age 5): FAILED")
        
    if comp_result2 and comp_result2.get("status") == "success":
        tests_passed += 1
        logger.info("✅ Comprehensive search (Burnaby, age 7): PASSED")
    else:
        logger.info("❌ Comprehensive search (Burnaby, age 7): FAILED")
        
    if pattern_result and pattern_result.get("status") == "success":
        tests_passed += 1
        logger.info("✅ Pattern discovery: PASSED")
    else:
        logger.info("❌ Pattern discovery: FAILED")
        
    if multi_result and multi_result.get("status") == "success":
        tests_passed += 1
        logger.info("✅ Multi-facility search: PASSED")
    else:
        logger.info("❌ Multi-facility search: FAILED")
    
    logger.info(f"\n🏁 Tests passed: {tests_passed}/{tests_total}")
    
    if tests_passed == tests_total:
        logger.info("🎉 All tests passed! The enhanced search is working correctly.")
    else:
        logger.info("⚠️ Some tests failed. Check the logs above for details.")

if __name__ == "__main__":
    asyncio.run(main())
