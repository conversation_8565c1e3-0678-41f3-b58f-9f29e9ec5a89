"""
Test Targon API integration with direct client
"""

import asyncio
import os
import sys
import logging
from dotenv import load_dotenv

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

async def test_targon_client():
    """Test direct Targon client."""
    from multi_tool_agent.utils.targon_client import get_targon_client
    
    try:
        # Get Targon client
        client = get_targon_client()
        logger.info(f"✅ Targon client initialized successfully")
        
        # Test with a simple message
        messages = [{"role": "user", "content": "Hello! Please respond with 'Hi there!' to confirm you're working."}]
        
        response_chunks = []
        logger.info("📡 Sending test message to Targon...")
        
        async for chunk in client.stream_completion(
            model="moonshotai/Kimi-K2-Instruct",
            messages=messages,
            temperature=0.7,
            max_tokens=100
        ):
            response_chunks.append(chunk)
            print(chunk, end="", flush=True)
        
        print()  # New line after streaming
        
        full_response = "".join(response_chunks)
        logger.info(f"✅ Response received: {len(full_response)} characters")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Targon client error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_rate_limited_model():
    """Test rate-limited model with Targon fallback."""
    from multi_tool_agent.models.rate_limited_llm import create_reasoning_model
    from multi_tool_agent.utils.rate_limiter import get_hybrid_manager
    
    try:
        # Force rate limit on Novita to test Targon fallback
        manager = get_hybrid_manager()
        
        # Simulate rate limit on Novita
        await manager.kimi_limiter.record_rate_limit_error()
        logger.info("🚫 Simulated rate limit on Novita Kimi")
        
        # Create a reasoning model
        model = create_reasoning_model()
        logger.info("✅ Created rate-limited reasoning model")
        
        # Test with ADK-style request (simplified)
        class MockRequest:
            def __init__(self, text):
                self.parts = [MockPart(text)]
        
        class MockPart:
            def __init__(self, text):
                self.text = text
        
        request = MockRequest("Hello! Can you confirm you're working by saying 'Hi there!'?")
        
        logger.info("📡 Testing rate-limited model with Targon fallback...")
        response_count = 0
        
        async for response in model.generate_content_async(request, stream=True):
            response_count += 1
            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate.content, 'parts') and candidate.content.parts:
                    for part in candidate.content.parts:
                        if hasattr(part, 'text'):
                            print(part.text, end="", flush=True)
        
        print()  # New line after streaming
        logger.info(f"✅ Received {response_count} response chunks")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Rate-limited model error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    logger.info("🚀 Starting Targon integration tests...")
    
    # Check if Targon is enabled
    if not os.getenv('USE_TARGON_KIMI', 'False').lower() == 'true':
        logger.warning("⚠️ USE_TARGON_KIMI is not enabled in .env")
        logger.info("Please set USE_TARGON_KIMI=True in your .env file")
        return
    
    # Check if API key is set
    if not os.getenv('TARGON_API_KEY'):
        logger.error("❌ TARGON_API_KEY not found in environment")
        logger.info("Please set TARGON_API_KEY in your .env file")
        return
    
    logger.info(f"📋 Targon API Base: {os.getenv('TARGON_API_BASE', 'Not set')}")
    logger.info(f"📋 Targon Model: {os.getenv('TARGON_KIMI_MODEL', 'Not set')}")
    
    # Test 1: Direct Targon client
    logger.info("\n=== Test 1: Direct Targon Client ===")
    success1 = await test_targon_client()
    
    # Test 2: Rate-limited model with fallback
    logger.info("\n=== Test 2: Rate-Limited Model with Targon Fallback ===")
    success2 = await test_rate_limited_model()
    
    # Summary
    logger.info("\n=== Test Summary ===")
    logger.info(f"Direct Targon Client: {'✅ PASSED' if success1 else '❌ FAILED'}")
    logger.info(f"Rate-Limited Model: {'✅ PASSED' if success2 else '❌ FAILED'}")
    
    if success1 and success2:
        logger.info("\n🎉 All tests passed! Targon integration is working correctly.")
    else:
        logger.error("\n⚠️ Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
