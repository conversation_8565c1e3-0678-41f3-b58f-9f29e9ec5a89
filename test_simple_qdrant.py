#!/usr/bin/env python3
"""
Simple Qdrant connection test
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from qdrant_client import AsyncQdrantClient, models
from multi_tool_agent.config import AgentConfig

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_connection():
    """Test basic connection to Qdrant Cloud."""
    try:
        logger.info("🔗 Testing connection to Qdrant Cloud...")
        
        client = AsyncQdrantClient(
            url=AgentConfig.QDRANT_URL,
            api_key=AgentConfig.QDRANT_API_KEY
        )
        
        # Test basic connection
        collections = await client.get_collections()
        logger.info(f"✅ Connected! Found {len(collections.collections)} collections")
        
        # Test collection info
        collection_info = await client.get_collection(AgentConfig.QDRANT_COLLECTION_NAME)
        logger.info(f"📊 Collection '{AgentConfig.QDRANT_COLLECTION_NAME}' has {collection_info.points_count} points")
        
        await client.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Connection failed: {e}")
        return False

async def test_simple_search():
    """Test simple vector search."""
    try:
        logger.info("🔍 Testing simple search...")
        
        client = AsyncQdrantClient(
            url=AgentConfig.QDRANT_URL,
            api_key=AgentConfig.QDRANT_API_KEY
        )
        
        # Simple search using dense vector
        search_response = await client.query_points(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            query=models.Document(
                text="swimming",
                model=AgentConfig.EMBEDDING_MODEL
            ),
            using="dense",
            limit=3,
            with_payload=True
        )
        
        results = search_response.points
        logger.info(f"✅ Found {len(results)} results")
        
        for i, point in enumerate(results):
            metadata = point.payload.get('metadata', {})
            name = metadata.get('name', 'Unknown')
            logger.info(f"  {i+1}. {name}")
        
        await client.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Search failed: {e}")
        return False

async def main():
    """Main test function."""
    print("🧪 Simple Qdrant Cloud Test")
    print("=" * 30)
    
    # Test 1: Connection
    success1 = await test_connection()
    print()
    
    # Test 2: Search
    if success1:
        success2 = await test_simple_search()
    else:
        success2 = False
    
    print()
    if success1 and success2:
        logger.info("🎉 All tests passed!")
    else:
        logger.error("❌ Some tests failed")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n❌ Test interrupted")
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
