#!/usr/bin/env python3
"""
Test Zero-Download Architecture
Verify that search works without any local model downloads using Qdrant Cloud.
"""

import os
import logging
from dotenv import load_dotenv
from qdrant_client import QdrantClient, models

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_zero_download():
    """Test zero-download architecture with Qdrant Cloud."""

    # Get configuration from environment
    QDRANT_URL = os.environ.get("QDRANT_URL")
    QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")
    COLLECTION_NAME = "community_activities_v2"
    EMBEDDING_MODEL = "BAAI/bge-base-en-v1.5"

    if not QDRANT_URL or not QDRANT_API_KEY:
        logger.error("❌ Missing QDRANT_URL or QDRANT_API_KEY environment variables")
        logger.info("💡 Please set up your .env file with:")
        logger.info("   QDRANT_URL=https://your-cluster.qdrant.tech")
        logger.info("   QDRANT_API_KEY=your-api-key")
        return False

    try:
        logger.info("🔗 Connecting to Qdrant Cloud...")
        logger.info(f"URL: {QDRANT_URL}")

        client = QdrantClient(
            url=QDRANT_URL,
            api_key=QDRANT_API_KEY
        )

        # Test 1: Collection info
        logger.info("📋 Test 1: Collection Information")
        collection_info = client.get_collection(COLLECTION_NAME)
        logger.info(f"📊 Collection: {COLLECTION_NAME}")
        logger.info(f"   Points: {collection_info.points_count}")
        logger.info(f"   Vectors: {list(collection_info.config.params.vectors.keys())}")

        if collection_info.points_count == 0:
            logger.warning("⚠️ Collection exists but has no data points")
            logger.info("💡 Run: python multi_tool_agent/update_qdrant_activities.py")
            return False

        print()

        # Test 2: Server-side embedding search
        logger.info("🔍 Test 2: Server-side Embedding Search")
        search_response = client.query_points(
            collection_name=COLLECTION_NAME,
            query=models.Document(
                text="swimming lessons for children",
                model=EMBEDDING_MODEL  # Server-side embedding generation
            ),
            using="dense",  # Use dense vector
            limit=5,
            with_payload=True
        )

        results = search_response.points
        logger.info(f"✅ Found {len(results)} results using server-side embeddings")

        for i, point in enumerate(results[:3]):
            # Try multiple ways to access the name
            payload = point.payload
            name = (
                payload.get('name') or
                payload.get('metadata', {}).get('name') or
                payload.get('activity_name') or
                'Unknown'
            )
            score = point.score
            logger.info(f"  {i+1}. {name} (score: {score:.3f})")

            # Debug: show payload keys for first result
            if i == 0:
                logger.info(f"     Debug - Payload keys: {list(payload.keys())}")
                if 'metadata' in payload:
                    metadata = payload['metadata']
                    if isinstance(metadata, dict):
                        logger.info(f"     Debug - Metadata keys: {list(metadata.keys())[:5]}...")  # Show first 5 keys

        print()
        logger.info("🎉 Zero-download search working perfectly!")
        logger.info("💡 No local models needed - Qdrant Cloud handles everything server-side")
        logger.info("🚀 Ultra-fast RAG system is ready for production!")

        return True

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🤖 Testing Zero-Download Architecture with Qdrant Cloud")
    print("=" * 60)

    success = test_zero_download()

    if success:
        logger.info("🎉 All tests passed!")
    else:
        logger.error("❌ Tests failed")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Test interrupted")
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
