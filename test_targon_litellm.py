#!/usr/bin/env python3
"""
Test script to verify Targon API compatibility with LiteLLM and ADK.
"""

import os
import asyncio
import litellm
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure LiteLLM for Targon
# Method 1: Using OpenAI-compatible endpoint
def test_targon_openai_format():
    """Test Targon using OpenAI-compatible format with LiteLLM"""
    print("🧪 Testing Targon with LiteLLM (OpenAI format)...")
    
    # Set Targon API key
    targon_api_key = os.getenv("TARGON_API_KEY", "your-targon-api-key")
    
    try:
        # Configure for OpenAI-compatible endpoint
        response = litellm.completion(
            model="openai/moonshotai/Kimi-K2-Instruct",  # OpenAI prefix tells LiteLLM to use OpenAI format
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Say hello in one sentence."}
            ],
            api_base="https://api.targon.com/v1",
            api_key=targon_api_key,
            temperature=0.7,
            max_tokens=50
        )
        
        print("✅ Success! Response:", response.choices[0].message.content)
        return True
        
    except Exception as e:
        print(f"❌ Error with OpenAI format: {e}")
        return False

# Method 2: Using custom provider format
def test_targon_custom_provider():
    """Test Targon as a custom provider in LiteLLM"""
    print("\n🧪 Testing Targon with LiteLLM (Custom Provider)...")
    
    targon_api_key = os.getenv("TARGON_API_KEY", "your-targon-api-key")
    
    # Set environment variables for custom provider
    os.environ["TARGON_API_KEY"] = targon_api_key
    os.environ["TARGON_API_BASE"] = "https://api.targon.com/v1"
    
    # Register Targon as a custom provider
    litellm.register_model({
        "targon/kimi-k2": {
            "max_tokens": 128000,
            "max_input_tokens": 128000,
            "max_output_tokens": 8192,
            "input_cost_per_token": 0.0,  # Update with actual costs
            "output_cost_per_token": 0.0,
            "litellm_provider": "openai",
            "base_url": "https://api.targon.com/v1",
            "api_key": targon_api_key,
            "model": "moonshotai/Kimi-K2-Instruct"
        }
    })
    
    try:
        response = litellm.completion(
            model="targon/kimi-k2",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "What is 2+2?"}
            ],
            temperature=0.7,
            max_tokens=50
        )
        
        print("✅ Success! Response:", response.choices[0].message.content)
        return True
        
    except Exception as e:
        print(f"❌ Error with custom provider: {e}")
        return False

# Method 3: Test streaming
async def test_targon_streaming():
    """Test Targon with streaming responses"""
    print("\n🧪 Testing Targon with streaming...")
    
    targon_api_key = os.getenv("TARGON_API_KEY", "your-targon-api-key")
    
    try:
        response = await litellm.acompletion(
            model="openai/moonshotai/Kimi-K2-Instruct",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Count from 1 to 5."}
            ],
            api_base="https://api.targon.com/v1",
            api_key=targon_api_key,
            stream=True,
            max_tokens=50
        )
        
        print("✅ Streaming response: ", end="")
        async for chunk in response:
            if chunk.choices[0].delta.content:
                print(chunk.choices[0].delta.content, end="")
        print()
        return True
        
    except Exception as e:
        print(f"❌ Error with streaming: {e}")
        return False

# Test ADK compatibility
def create_test_litellm_model():
    """Create a LiteLLM model instance for ADK testing"""
    from google.adk.models.lite_llm import LiteLlm
    
    print("\n🧪 Testing Targon with ADK LiteLlm...")
    
    targon_api_key = os.getenv("TARGON_API_KEY", "your-targon-api-key")
    
    # Configure environment for Targon
    os.environ["TARGON_API_KEY"] = targon_api_key
    os.environ["TARGON_API_BASE"] = "https://api.targon.com/v1"
    
    try:
        # Create ADK LiteLlm instance with Targon
        model = LiteLlm(
            model="openai/moonshotai/Kimi-K2-Instruct",
            api_base="https://api.targon.com/v1",
            api_key=targon_api_key
        )
        
        print("✅ ADK LiteLlm model created successfully!")
        return model
        
    except Exception as e:
        print(f"❌ Error creating ADK model: {e}")
        return None

async def main():
    """Run all tests"""
    print("🚀 Starting Targon compatibility tests...")
    print("=" * 50)
    
    # Check for API key
    if not os.getenv("TARGON_API_KEY"):
        print("⚠️ TARGON_API_KEY not found in environment variables!")
        print("Please set it in your .env file or environment")
        return
    
    # Test 1: OpenAI format
    test_targon_openai_format()
    
    # Test 2: Custom provider
    test_targon_custom_provider()
    
    # Test 3: Streaming
    await test_targon_streaming()
    
    # Test 4: ADK compatibility
    try:
        create_test_litellm_model()
    except ImportError:
        print("\n⚠️ ADK not available, skipping ADK test")
    
    print("\n✅ All tests completed!")
    print("\n📝 Next steps:")
    print("1. Add TARGON_API_KEY to your .env file")
    print("2. Update config.py to include Targon as a fallback")
    print("3. Modify rate_limiter.py to use Targon when Novita is rate limited")

if __name__ == "__main__":
    asyncio.run(main())
