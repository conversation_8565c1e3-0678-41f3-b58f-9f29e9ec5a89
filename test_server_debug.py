"""
Test server with detailed debugging to capture errors
"""
import asyncio
import logging
import sys
from adk_server import app, runner, session_service, APP_NAME
from google.genai.types import Part, Content
import traceback

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('server_debug.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

async def test_agent_directly():
    """Test the agent directly without WebSocket to capture errors"""
    logger.info("🧪 Testing agent directly...")
    
    try:
        # Create a test session
        user_id = "test_user"
        session = await session_service.create_session(app_name=APP_NAME, user_id=user_id)
        logger.info(f"✅ Created session: {session.id}")
        
        # Create test content
        test_message = Content(
            parts=[Part(text="What swimming classes are available for kids?")],
            role="user"
        )
        
        logger.info("📤 Running agent...")
        response_count = 0
        
        # Run the agent
        async for event in runner.run_async(
            new_message=test_message,
            user_id=user_id,
            session_id=session.id
        ):
            response_count += 1
            logger.info(f"📥 Event #{response_count}: {type(event)}")
            
            # Log event details
            if hasattr(event, 'content'):
                logger.info(f"   Content: {event.content}")
            if hasattr(event, 'partial'):
                logger.info(f"   Partial: {event.partial}")
            if hasattr(event, 'error'):
                logger.info(f"   Error: {event.error}")
                
            # Stop after a few events to avoid long output
            if response_count >= 5:
                logger.info("   ... (stopping after 5 events)")
                break
                
        logger.info(f"✅ Agent completed with {response_count} events")
        
    except Exception as e:
        logger.error(f"❌ Error during agent execution: {type(e).__name__}: {str(e)}")
        logger.error(f"Traceback:\n{traceback.format_exc()}")
        
async def main():
    """Run the test"""
    logger.info("🚀 Starting direct agent test...")
    
    # Test the agent
    await test_agent_directly()
    
    # Check API stats
    logger.info("\n🔍 Checking API stats...")
    try:
        from multi_tool_agent.utils.api_monitor import get_api_monitor
        monitor = get_api_monitor()
        stats = monitor.get_current_stats()
        logger.info(f"📊 Total API calls: {stats['total_calls']}")
        logger.info(f"🧠 Kimi calls: {stats['kimi_calls']}")
        logger.info(f"⚡ Fallback calls: {stats['fallback_calls']}")
    except Exception as e:
        logger.error(f"Failed to get API stats: {e}")

if __name__ == "__main__":
    asyncio.run(main())
