#!/usr/bin/env python3
"""
ADK-based FastAPI WebSocket Server for Multi-Tool Agent
Based on ADK Streaming patterns with WebSocket support
"""

import os
import json
import asyncio
import base64
import time
import jwt
from pathlib import Path
from dotenv import load_dotenv
from datetime import datetime, timedelta
import logging
import re
from typing import AsyncGenerator, Dict, Any, List, Optional

from google.genai.types import Part, Content, Blob
from google.adk.runners import Runner
from google.adk.agents import LiveRequestQueue, Agent
from google.adk.agents.run_config import RunConfig, StreamingMode
from google.adk.sessions.in_memory_session_service import InMemorySessionService
from google.adk.tools import google_search

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse, StreamingResponse, Response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from fastapi import BackgroundTasks

# Load environment variables
load_dotenv()

# --- Authentication Configuration ---
JWT_SECRET = os.getenv("JWT_SECRET", "your-super-secret-jwt-key-change-in-production")
JWT_ALGORITHM = "HS256"
JWT_EXPIRY_HOURS = 24
GOOGLE_CLIENT_IDS = [cid.strip() for cid in os.getenv("GOOGLE_CLIENT_ID", "").split(",") if cid.strip()]

# Security scheme for JWT tokens
security = HTTPBearer()

# Configure logging first
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Import Your Sophisticated Multi-Tool Agent ---
from multi_tool_agent.orchestrator import root_agent
from multi_tool_agent.config import AgentConfig

# Environment variables for LiteLLM are configured in rate_limited_llm.py
if AgentConfig.NOVITA_API_KEY:
    logger.info(f"✅ Configured LiteLLM for Novita API at {AgentConfig.NOVITA_API_BASE}")

# Application configuration
APP_NAME = "multi_tool_agent"

# --- Session ID Mapping ---
# Maps frontend session IDs to ADK-generated session IDs
# In production, use Redis, database, or other persistent store
session_id_map = {}

print(" Starting Multi-Tool Agent ADK Server...")
print(" Using your sophisticated BC Activity Assistant with:")
print("    Graphiti Knowledge Graph")
print("    Enhanced Memory System") 
print("    BC Activity Specialization")
print("    Advanced Tools (url_context_search, OAuth, etc.)")
print("    Background Save Queue")
print(" Build stamp:", os.getenv("BUILD_STAMP", "local-dev"))

# Quick diagnostic to ensure critical env vars are present when container starts
if os.getenv("GOOGLE_API_KEY"):
    print(" GOOGLE_API_KEY detected")
else:
    print("  GOOGLE_API_KEY is NOT set – google_search tool will be disabled")

# --- Session Service (using same infrastructure as :8001) ---
try:
    session_service = InMemorySessionService()
    logger.info("✅ Session service initialized - same infrastructure as :8001")
except Exception as e:
    logger.error(f"❌ Failed to initialize session service: {e}")
    session_service = None

# --- Runner Setup (using your existing agent) ---
try:
    runner = Runner(
        session_service=session_service,
        app_name=APP_NAME,
        agent=root_agent
    )
    logger.info("✅ Runner initialized with your sophisticated BC Activity Assistant")
except Exception as e:
    logger.error(f"❌ Failed to initialize runner: {e}")
    runner = None

# FastAPI app
app = FastAPI(
    title="Multi-Tool Agent API",
    description="ADK-powered streaming agent with WebSocket support",
    version="1.0.0"
)

# CORS middleware for React frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "agent": "BC Activity Assistant (Multi-Tool)",
        "features": [
            "Graphiti Knowledge Graph",
            "Enhanced Memory System",
            "BC Activity Specialization",
            "Advanced Tools Suite",
            "Background Save Queue",
            "Rate Limiting & Hybrid Models"
        ],
        "timestamp": datetime.now().isoformat()
    }

# API monitoring endpoint
@app.get("/api-stats")
async def api_stats():
    """Get API usage statistics"""
    try:
        from multi_tool_agent.utils.api_monitor import get_api_monitor
        from multi_tool_agent.utils.request_queue import get_global_queue

        monitor = get_api_monitor()
        queue = get_global_queue()

        return {
            "api_usage": monitor.get_current_stats(),
            "request_queue": queue.get_stats(),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

# Memory/sessions endpoint
@app.get("/memory/sessions")
async def get_sessions():
    """Get available sessions"""
    return JSONResponse({
        "sessions": [],
        "total": 0,
        "message": "Sessions endpoint for sophisticated agent"
    })

# Pydantic models for chat API
class ChatMessage(BaseModel):
    role: str
    content: str

class ChatRequest(BaseModel):
    messages: list[ChatMessage]
    session_id: str = "default"
    id: str = None
    previewToken: str = None

# --- Authentication Models ---
class GoogleAuthRequest(BaseModel):
    google_token: str

class AuthResponse(BaseModel):
    user_id: str
    session_token: str
    user_profile: dict
    expires_at: int

class UserProfile(BaseModel):
    google_id: str
    email: str
    name: str
    given_name: str
    family_name: str
    picture: Optional[str] = None
    email_verified: bool = False

# --- JWT Token Management ---
def create_session_token(user_profile: dict) -> str:
    """Create JWT token with user claims"""
    payload = {
        "sub": user_profile["google_id"],  # Google user ID
        "email": user_profile["email"],
        "name": user_profile["name"],
        "iat": int(time.time()),
        "exp": int(time.time()) + (JWT_EXPIRY_HOURS * 3600)
    }
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

def verify_jwt_token(token: str) -> dict:
    """Verify JWT token and return claims"""
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token has expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

def generate_user_id(google_user_id: str) -> str:
    """Generate consistent user_id from Google ID"""
    return f"user_{google_user_id}"

def create_confirmation_response_content(msg: Dict[str, Any]) -> Content:
    """Creates the ADK Content object for a user's confirmation response."""
    from google.genai.types import FunctionResponse

    confirmation_id = msg.get("confirmation_id")
    user_decision = msg.get("decision") # e.g., "yes" or "no"

    if not confirmation_id or not user_decision:
        # Handle error, maybe return an error Content object or raise
        raise ValueError("Missing confirmation_id or decision in confirmation response.")
    
    logger.info(f"User responded to confirmation '{confirmation_id}' with: '{user_decision}'")

    # The agent expects a dictionary with a 'result' key
    response_payload = {'result': user_decision}

    return Content(
        role='user', # A FunctionResponse is sent with the 'user' role
        parts=[
            Part(
                function_response=FunctionResponse(
                    name='get_user_confirmation',
                    id=confirmation_id,
                    response=response_payload
                )
            )
        ]
    )

# --- Authentication Middleware ---
def get_authenticated_user_id(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Extract authenticated user ID from JWT token"""
    try:
        claims = verify_jwt_token(credentials.credentials)
        return generate_user_id(claims['sub'])
    except Exception:
        raise HTTPException(status_code=401, detail="Authentication required")

def get_authenticated_user_id_optional(request: Request) -> Optional[str]:
    """Extract user ID from token if present, return None if not authenticated"""
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return None
        
        token = auth_header[7:]  # Remove "Bearer "
        claims = verify_jwt_token(token)
        return generate_user_id(claims['sub'])
    except Exception:
        return None

# --- Authentication Endpoints ---
@app.post("/auth/google", response_model=AuthResponse)
async def authenticate_google(auth_request: GoogleAuthRequest):
    """Authenticate user with Google ID token"""
    try:
        # Import Google OAuth verification
        from google.oauth2 import id_token
        from google.auth.transport import requests
        
        if not GOOGLE_CLIENT_IDS:
            raise HTTPException(status_code=500, detail="Google authentication not configured")
        
        # Verify the Google token
        try:
            # First verify the token signature (no audience check)
            idinfo = id_token.verify_oauth2_token(
                auth_request.google_token,
                requests.Request(),
                None,
            )

            # Now enforce audience ourselves against configured list
            aud = idinfo.get("aud")
            if aud not in GOOGLE_CLIENT_IDS:
                raise ValueError(
                    f"Token audience {aud} not in allowed client IDs {GOOGLE_CLIENT_IDS}"
                )
            
            # Verify issuer
            if idinfo['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
                raise ValueError('Invalid token issuer')
                
        except ValueError as e:
            raise HTTPException(status_code=401, detail=f"Invalid Google token: {e}")
        
        # Extract user information
        user_profile = {
            "google_id": idinfo['sub'],
            "email": idinfo.get('email', ''),
            "name": idinfo.get('name', ''),
            "given_name": idinfo.get('given_name', ''),
            "family_name": idinfo.get('family_name', ''),
            "picture": idinfo.get('picture', ''),
            "email_verified": idinfo.get('email_verified', False)
        }
        
        # Generate our internal user ID
        user_id = generate_user_id(user_profile["google_id"])
        
        # Create JWT session token
        session_token = create_session_token(user_profile)
        expires_at = int(time.time()) + (JWT_EXPIRY_HOURS * 3600)
        
        logger.info(f"✅ User authenticated: {user_profile['email']} -> {user_id}")
        
        return AuthResponse(
            user_id=user_id,
            session_token=session_token,
            user_profile=user_profile,
            expires_at=expires_at
        )
        
    except ImportError:
        raise HTTPException(status_code=500, detail="Google authentication libraries not available")
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise HTTPException(status_code=500, detail=f"Authentication failed: {str(e)}")

@app.get("/auth/profile", response_model=UserProfile)
async def get_user_profile(user_id: str = Depends(get_authenticated_user_id)):
    """Get current user profile information"""
    try:
        # In a real system, this would fetch from database
        # For now, extract from token claims
        return UserProfile(
            google_id=user_id.replace("user_", ""),
            email="<EMAIL>",  # Would come from database
            name="User Name",  # Would come from database
            given_name="User",
            family_name="Name",
            email_verified=True
        )
    except Exception as e:
        logger.error(f"Profile fetch error: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch profile")

@app.post("/auth/logout")
async def logout(user_id: str = Depends(get_authenticated_user_id)):
    """Logout user and invalidate session"""
    try:
        # In production, add token to blacklist
        logger.info(f"🚪 User logged out: {user_id}")
        return {"status": "success", "message": "Logged out successfully"}
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(status_code=500, detail="Logout failed")

# Store active SSE response queues for sending events to specific clients
active_sse_streams: Dict[str, asyncio.Queue] = {}

async def sse_event_sender(session_id: str):
    """Generator for a specific client's SSE connection."""
    queue = asyncio.Queue()
    active_sse_streams[session_id] = queue
    logger.info(f"✅ SSE sender registered for session: {session_id}")
    
    try:
        # Send initial connection event
        yield create_sse_event("connection", {
            "status": "connected", 
            "session_id": session_id,
            "message": "Connected to BC Parent Activity Assistant"
        }, session_id)
        
        # Send test event to verify connection
        yield create_sse_event("test", {
            "message": "SSE connection working", 
            "session_id": session_id
        }, session_id)
        
        while True:
            try:
                # Wait for next event, but unblock every 15 s to send heartbeat
                event_to_send = await asyncio.wait_for(queue.get(), timeout=15)
                if event_to_send is None:  # Signal to close
                    logger.info(f"🔚 Closing SSE sender for session: {session_id}")
                    break
                yield event_to_send
                queue.task_done()
            except asyncio.TimeoutError:
                # Send comment heartbeat to keep connection alive and avoid 502 truncation
                yield ": heartbeat\n\n"
            
    except asyncio.CancelledError:
        logger.info(f"❌ SSE sender for session {session_id} cancelled.")
    finally:
        if session_id in active_sse_streams:
            del active_sse_streams[session_id]
            logger.info(f"🧹 SSE sender deregistered for session: {session_id}")

# SSE GET endpoint for EventSource connections (renamed for clarity)
@app.get("/api/chat_stream_connect")
async def chat_stream_connect_endpoint(session_id: str):
    """
    SSE endpoint for EventSource connections (GET)
    Establishes and maintains the SSE connection for real-time updates
    """
    logger.info(f"🌊 EventSource connecting for session: {session_id}")
    
    return StreamingResponse(
        sse_event_sender(session_id),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "http://localhost:5173",
            "Access-Control-Allow-Headers": "Content-Type",
            "Access-Control-Allow-Methods": "GET",
        }
    )

async def run_adk_and_push_to_sse(
    user_id: str, 
    effective_adk_session_id: str, 
    current_user_content: Content, 
    frontend_session_id: str
):
    """
    Run the ADK agent and push events to the SSE connection for the given session
    """
    sse_queue = active_sse_streams.get(frontend_session_id)
    if not sse_queue:
        logger.error(f"No active SSE queue for session: {frontend_session_id}")
        return

    try:
        logger.info(f"Running ADK agent for session {frontend_session_id} -> ADK session {effective_adk_session_id}")
        
        async for adk_event in runner.run_async(
            new_message=current_user_content,
            user_id=user_id,
            session_id=effective_adk_session_id,
            run_config=RunConfig(streaming_mode=StreamingMode.SSE)
        ):
            # Stream text chunks as they arrive
            agent_text = extract_agent_text_from_adk_event(adk_event)
            if agent_text:
                await sse_queue.put(create_sse_event("text_chunk", {
                    "content": agent_text,
                    "is_final": False
                }, frontend_session_id))

        # Send turn complete event
        await sse_queue.put(create_sse_event("turn_complete", {
            "session_id": frontend_session_id,
            "timestamp": time.time()
        }, frontend_session_id))
        
        logger.info(f"ADK agent completed for session {frontend_session_id}")

    except Exception as e:
        logger.error(f"Error in ADK agent execution: {e}")
        await sse_queue.put(create_sse_event("error", {
            "message": f"Agent error: {str(e)}",
            "details": str(e)
        }, frontend_session_id))

@app.post("/api/chat_message")
async def post_chat_message(request: ChatRequest, background_tasks: BackgroundTasks, req: Request):
    """
    New endpoint for sending messages that triggers the agent
    Agent responses are pushed via the existing SSE connection
    """
    # Get authenticated user ID (optional for development, required in production)
    user_id = get_authenticated_user_id_optional(req)
    if not user_id:
        # Development mode: fallback to default user
        # In production, this should raise HTTPException(401, "Authentication required")
        user_id = "user123"  # Development fallback
        logger.warning("No authentication provided, using development user ID")
    
    frontend_session_id = request.id or request.session_id or f"post_default_{int(time.time())}"
    
    logger.info(f"📬 Received message for session: {frontend_session_id} from user: {user_id}")

    if not request.messages:
        return JSONResponse(content={"error": "No messages provided"}, status_code=400)
    
    # Check if we have an active SSE connection for this session
    if frontend_session_id not in active_sse_streams:
        logger.error(f"❌ No active SSE stream for session {frontend_session_id}")
        return JSONResponse(content={"error": "No active SSE stream for this session"}, status_code=404)
    
    # Session ID mapping logic (same as before)
    effective_adk_session_id = session_id_map.get(frontend_session_id)
    adk_session = None
    
    if effective_adk_session_id:
        try:
            # Use correct keyword arguments for get_session
            adk_session = await session_service.get_session(
                app_name=APP_NAME, 
                user_id=user_id, 
                session_id=effective_adk_session_id
            )
        except Exception as e:
            logger.warning(f"Failed to get existing session: {e}")
            adk_session = None
            
        if not adk_session:
            del session_id_map[frontend_session_id]
            effective_adk_session_id = None
    
    if not adk_session:
        try:
            # Use keyword-only arguments as required by ADK API
            adk_session = await session_service.create_session(app_name=APP_NAME, user_id=user_id)
            effective_adk_session_id = adk_session.id
            session_id_map[frontend_session_id] = effective_adk_session_id
            logger.info(f"📋 Created ADK session {effective_adk_session_id} for frontend {frontend_session_id} (user: {user_id})")
        except Exception as e:
            logger.error(f"Failed to create ADK session: {e}")
            return JSONResponse(content={"error": f"Failed to create session: {str(e)}"}, status_code=500)
    
    # Prepare user content
    current_user_input_text = request.messages[-1].content
    current_user_content = Content(parts=[Part(text=current_user_input_text)], role="user")
    
    # Run ADK agent in background and push events to SSE
    background_tasks.add_task(
        run_adk_and_push_to_sse,
        user_id,
        effective_adk_session_id,
        current_user_content,
        frontend_session_id
    )
    
    return JSONResponse(content={
        "status": "message_received",
        "session_id": frontend_session_id,
        "user_id": user_id
    }, status_code=202)

# WebSocket endpoint
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """Bidirectional WebSocket: client sends user message JSON, server streams ADK events back"""
    await websocket.accept()
    logger.info("✅ WebSocket connected")

    async def ping_loop():
        """send ping every 30 s so Cloud Run doesn't drop idle socket"""
        try:
            while True:
                await asyncio.sleep(30)
                await websocket.send_json({"type": "ping", "ts": time.time()})
        except Exception:
            # socket closed
            pass

    ping_task = asyncio.create_task(ping_loop())

    try:
        while True:
            raw = await websocket.receive_text()
            try:
                msg = json.loads(raw)
            except json.JSONDecodeError:
                await websocket.send_json({"type": "error", "message": "Invalid JSON"})
                continue

            # --- NEW: Handle different message types from frontend ---
            msg_type = msg.get("type", "chat_message") # Default to chat message

            if msg_type == "confirmation_response":
                # This is the user clicking "Yes" or "No"
                try:
                    content = create_confirmation_response_content(msg)
                except ValueError as e:
                    await websocket.send_json({"type": "error", "message": str(e)})
                    continue
            else: # It's a standard chat message
                content_text = msg.get("content")
                if not content_text:
                    await websocket.send_json({"type": "error", "message": "No content provided"})
                    continue
                content = Content(parts=[Part(text=content_text)], role="user")

            frontend_session_id = msg.get("session_id", "ws_default")
            user_id = msg.get("user_id", "user_ws")

            # Session ID mapping logic for WebSocket, similar to SSE endpoint
            effective_adk_session_id = session_id_map.get(frontend_session_id)
            adk_session = None
            
            if effective_adk_session_id:
                try:
                    adk_session = await session_service.get_session(
                        app_name=APP_NAME, 
                        user_id=user_id, 
                        session_id=effective_adk_session_id
                    )
                except Exception as e:
                    logger.warning(f"Failed to get existing session for WebSocket: {e}")
                    adk_session = None
                    
                if not adk_session:
                    del session_id_map[frontend_session_id]
                    effective_adk_session_id = None
            
            if not adk_session:
                try:
                    adk_session = await session_service.create_session(app_name=APP_NAME, user_id=user_id)
                    effective_adk_session_id = adk_session.id
                    session_id_map[frontend_session_id] = effective_adk_session_id
                    logger.info(f"📋 Created ADK session {effective_adk_session_id} for WebSocket frontend {frontend_session_id} (user: {user_id})")
                except Exception as e:
                    logger.error(f"Failed to create ADK session for WebSocket: {e}")
                    await websocket.send_json({"type": "error", "message": f"Failed to create session: {str(e)}"})
                    continue

            # quick ack
            await websocket.send_json({"type": "ack", "session_id": frontend_session_id})

            # --- MODIFIED: Agent execution loop with confirmation handling ---
            streaming_response_parts = []
            final_response_text = None
            confirmation_pending = False

            try:
                async for adk_event in runner.run_async(
                    new_message=content,
                    user_id=user_id,
                    session_id=effective_adk_session_id,
                    run_config=RunConfig(streaming_mode=StreamingMode.SSE)
                ):
                    # --- NEW: DETECT CONFIRMATION REQUEST ---
                    function_calls = adk_event.get_function_calls()
                    if function_calls:
                        for fc in function_calls:
                            if fc.name == "get_user_confirmation":
                                plan_to_confirm = fc.args.get("plan")
                                await websocket.send_json({
                                    "type": "confirmation_request",
                                    "plan": plan_to_confirm,
                                    "confirmation_id": fc.id # Pass the ID to the frontend
                                })
                                confirmation_pending = True
                                break # Stop processing events for this turn
                    if confirmation_pending:
                        break
                    # --- END NEW ---

                    # Handle normal text streaming
                    if adk_event.partial and adk_event.content and adk_event.content.parts and adk_event.content.parts[0].text:
                        chunk = adk_event.content.parts[0].text
                        streaming_response_parts.append(chunk)
                        await websocket.send_json({"type": "text_chunk", "content": chunk, "is_final": False})

                    # Handle final response
                    if adk_event.is_final_response():
                        # Case 1: Skipped Summarization (Your exact scenario)
                        if adk_event.actions.skip_summarization and adk_event.get_function_responses():
                            tool_result_dict = adk_event.get_function_responses()[0].response
                            if 'result' in tool_result_dict:
                                final_response_text = tool_result_dict['result']
                            else:
                                final_response_text = "Tool ran but didn't provide a 'result'."
                        
                        # Case 2: End of a text stream
                        elif streaming_response_parts:
                            # Append the very last chunk if it exists
                            if adk_event.content and adk_event.content.parts and adk_event.content.parts[0].text:
                                streaming_response_parts.append(adk_event.content.parts[0].text)
                            raw_response = "".join(streaming_response_parts)
                            # Apply final post-processing to clean artifacts
                            final_response_text = final_response_post_processor(raw_response)

                        # Case 3: A single, non-streaming text response
                        elif adk_event.content and adk_event.content.parts and adk_event.content.parts[0].text:
                            raw_response = adk_event.content.parts[0].text.strip()
                            # Apply final post-processing to clean artifacts
                            final_response_text = final_response_post_processor(raw_response)
                        
                        # Once we determine the final text, break the loop
                        break
                    
            except Exception as e:
                logger.error(f"Error during agent execution: {type(e).__name__}: {str(e)}")
                await websocket.send_json({
                    "type": "error",
                    "message": f"Agent error: {str(e)}",
                    "error_type": type(e).__name__
                })
                continue

            # If a confirmation is NOT pending, send the final message and turn complete
            if not confirmation_pending:
                if final_response_text:
                    await websocket.send_json({
                        "type": "text_chunk",
                        "content": final_response_text,
                        "is_final": True,
                    })
                await websocket.send_json({"type": "turn_complete"})
            # If confirmation IS pending, we do nothing and just wait for the next user message

    except WebSocketDisconnect:
        logger.info("WebSocket disconnected")
    finally:
        ping_task.cancel()

# Alternative WebSocket endpoint with user ID parameter (ADK standard)
@app.websocket("/ws/{user_id}")
async def websocket_user_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint with user ID for sophisticated agent sessions"""
    await websocket.accept()
    logger.info(f"✅ WebSocket connected for user {user_id} (sophisticated agent)")
    
    try:
        while True:
            data = await websocket.receive_text()
            logger.info(f"[WS SOPHISTICATED {user_id}]: {data}")
            
            response = f"Sophisticated agent for user {user_id}: {data}"
            await websocket.send_text(response)
            
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for user {user_id}")

# Root endpoint
@app.get("/")
async def root():
    """Simple status page"""
    return JSONResponse({
        "message": "Multi-Tool Agent ADK Server",
        "status": "running",
        "websocket_endpoints": ["/ws", "/ws/{user_id}"],
        "health_check": "/health"
    })

# Enhanced SSE Streaming for Custom UI
import time
import json
import re
from typing import AsyncGenerator, Dict, Any, List
from fastapi.responses import StreamingResponse

# SSE Event Types for Custom Frontend
SSE_EVENT_TYPES = {
    "connection": "Connection established",
    "text_chunk": "Agent text response chunk", 
    "tool_call_start": "Tool execution started",
    "tool_call_complete": "Tool execution completed",
    "activity_card": "Activity data extracted",
    "memory_update": "Memory information stored", # Corrected typo from "memmory"
    "error": "Error occurred",
    "turn_complete": "Conversation turn finished"
}

def create_sse_event(event_type: str, data: Dict[str, Any], session_id: str = None) -> str:
    """Create a properly formatted SSE event"""
    event_data = {
        "type": event_type,
        "data": data,
        "timestamp": time.time(),
        "sessionId": session_id
    }
    
    # Format as proper SSE event with event type and data
    # Important: Must have exactly two newlines at the end
    sse_message = f"event: {event_type}\ndata: {json.dumps(event_data)}\n\n"
    
    # Debug: Show exactly what we're sending
    logger.debug(f"SSE Event Format: {repr(sse_message)}")
    
    return sse_message

def extract_tool_name_from_adk_event(event) -> str:
    """Extract tool name from ADK event for status updates"""
    try:
        # Check if it's a function call event
        if hasattr(event, 'content') and hasattr(event.content, 'parts'):
            for part in event.content.parts:
                if hasattr(part, 'function_call') and hasattr(part.function_call, 'name'):
                    return part.function_call.name
        return "unknown_tool"
    except Exception:
        return "unknown_tool"

def get_tool_status_message(tool_name: str) -> str:
    """Get user-friendly status message for tool execution"""
    tool_messages = {
        'retrieve_info_from_memory': '🧠 Checking what I know about your family...',
        'url_context_search': '🔍 Searching for current BC activities...',
        'store_activity_preference': '💾 Saving your preferences...',
        'store_registration_info': '📝 Recording registration information...',
        'get_proactive_suggestions': '💡 Finding seasonal recommendations...',
        'get_current_time': '🕐 Getting current BC time...',
        'authenticate_with_google': '🔐 Authenticating with Google...'
    }
    return tool_messages.get(tool_name, f'🛠️ Using {tool_name}...')

def detect_tool_call_in_adk_event(event) -> bool:
    """Detect if an ADK event contains a tool call"""
    try:
        if hasattr(event, 'content') and hasattr(event.content, 'parts'):
            for part in event.content.parts:
                if hasattr(part, 'function_call'):
                    return True
        return False
    except Exception:
        return False

def clean_response_text(text: str) -> str:
    """
    Clean response text by removing tool call artifacts and system messages.

    Args:
        text: Raw response text that may contain artifacts

    Returns:
        Cleaned text suitable for user display
    """
    import re

    if not text:
        return text

    # Remove tool call artifacts - more comprehensive patterns
    patterns_to_remove = [
        # Kimi K2 specific artifacts
        r'<\|tool_calls_section.*?\|>',
        r'<\|tool_calls_sectio.*?\|>',  # Handle typos like "sectioall_end"
        r'<\|.*?_end\|>',
        r'<\|.*?_section_end\|>',
        r'<\|.*?all_end\|>',  # Handle "sectioall_end" pattern

        # Generic tool call patterns
        r'<tool_call>.*?</tool_call>',
        r'<function_calls>.*?</function_calls>',
        r'\[TOOL_CALL\].*?\[/TOOL_CALL\]',
        r'```json\s*\{[^}]*"function"[^}]*\}.*?```',

        # Model thinking patterns
        r'Let me search.*?<\|.*?\|>',
        r'I\'ll.*?<\|.*?\|>',

        # Repeated phrases that might indicate streaming issues
        r'(I\'ll find activities.*?New Westminster\.){2,}',
        r'(Let me search.*?){2,}',
    ]

    cleaned_text = text
    for pattern in patterns_to_remove:
        cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.DOTALL | re.IGNORECASE)

    # Remove excessive whitespace and newlines
    cleaned_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_text)
    cleaned_text = re.sub(r'^\s+|\s+$', '', cleaned_text)

    # Remove any remaining artifacts that might have been missed
    cleaned_text = re.sub(r'<\|[^|]*\|>', '', cleaned_text)

    return cleaned_text

def extract_agent_text_from_adk_event(event) -> str:
    """Extract text content from ADK agent response and clean it"""
    try:
        if hasattr(event, 'content') and hasattr(event.content, 'parts'):
            text_parts = []
            for part in event.content.parts:
                # Check if part has text AND is not a function call (function_call is None or doesn't exist)
                if hasattr(part, 'text') and part.text and (not hasattr(part, 'function_call') or part.function_call is None):
                    text_parts.append(part.text)

            raw_text = ''.join(text_parts)
            # Clean the text before returning
            cleaned_text = clean_response_text(raw_text)
            return cleaned_text
        return ""
    except Exception as e:
        logger.error(f"Error extracting text from ADK event: {e}")
        return ""

def final_response_post_processor(response_text: str) -> str:
    """
    Final post-processing of complete response to ensure it's clean for users.
    This is applied to the complete response after all streaming is done.
    """
    if not response_text:
        return response_text

    # Apply comprehensive cleaning
    cleaned = clean_response_text(response_text)

    # Additional final cleanup for any remaining artifacts
    import re

    # Remove any remaining tool call patterns that might have been missed
    final_patterns = [
        r'<\|[^|]*\|>',  # Any remaining <|...|> patterns
        r'Let me search.*?(?=\n\n|\n[A-Z]|$)',  # Remove "Let me search..." lines
        r'I\'ll find.*?(?=\n\n|\n[A-Z]|$)',  # Remove "I'll find..." lines
        r'^.*?<\|.*?\|>.*?$',  # Remove entire lines containing artifacts
    ]

    for pattern in final_patterns:
        cleaned = re.sub(pattern, '', cleaned, flags=re.MULTILINE | re.DOTALL)

    # Clean up any resulting empty lines or whitespace
    cleaned = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned)
    cleaned = re.sub(r'^\s+|\s+$', '', cleaned)

    # If the response is too short after cleaning, it might have been over-cleaned
    if len(cleaned.strip()) < 50 and len(response_text.strip()) > 100:
        logger.warning("Response may have been over-cleaned, using less aggressive cleaning")
        # Use only basic cleaning
        cleaned = clean_response_text(response_text)
        cleaned = re.sub(r'<\|[^|]*\|>', '', cleaned)

    return cleaned



# Memory visualization endpoint
@app.get("/api/sessions/debug")
async def get_sessions_debug(user_id: str = "user_102079780777749597961"):
    """Debug endpoint to examine session data and memory storage"""
    try:
        if not runner or not runner.session_service:
            return {"error": "Session service not available"}
        
        session_service = runner.session_service
        debug_info = {
            "session_investigation": {},
            "memory_analysis": {},
            "summary": {}
        }
        
        # Get session service type
        service_type = type(session_service).__name__
        debug_info["session_investigation"]["service_type"] = service_type
        
        logger.info(f"🔍 SESSION DEBUG: Using {service_type}")
        
        # List sessions for the user - need to find the app_name first
        try:
            # Try to get app_name from runner
            app_name = None
            if hasattr(runner, 'app_name'):
                app_name = runner.app_name
            elif hasattr(runner, '_app_name'):
                app_name = runner._app_name
            else:
                # Try common app names used by the agent
                possible_app_names = [
                    "bc_parent_activity_assistant", 
                    "BC Activity Assistant", 
                    "multi_tool_agent",
                    "adk_server"
                ]
                app_name = possible_app_names[0]  # Use first as default
            
            debug_info["session_investigation"]["app_name_used"] = app_name
            logger.info(f"Trying to list sessions with app_name: {app_name}")
            
            # Try multiple app names if the first one fails
            sessions = []
            app_name_tried = []
            possible_app_names = [
                app_name,
                "bc_parent_activity_assistant", 
                "BC Activity Assistant", 
                "multi_tool_agent",
                "adk_server",
                "default"
            ]
            
            for try_app_name in possible_app_names:
                if try_app_name and try_app_name not in app_name_tried:
                    try:
                        app_name_tried.append(try_app_name)
                        sessions = await session_service.list_sessions(user_id=user_id, app_name=try_app_name)
                        if sessions:
                            debug_info["session_investigation"]["successful_app_name"] = try_app_name
                            logger.info(f"SUCCESS: Found {len(sessions)} sessions with app_name: {try_app_name}")
                            break
                        else:
                            logger.info(f"No sessions found with app_name: {try_app_name}")
                    except Exception as e:
                        logger.info(f"Failed with app_name '{try_app_name}': {e}")
                        continue
            
            debug_info["session_investigation"]["app_names_tried"] = app_name_tried
            debug_info["session_investigation"]["sessions_found"] = len(sessions)
            debug_info["session_investigation"]["session_list"] = []
            
            logger.info(f"Found {len(sessions)} sessions for user {user_id}")
            
            # Examine each session
            for session in sessions:
                session_info = {
                    "id": session.id,
                    "app_name": session.app_name,
                    "user_id": session.user_id,
                    "events_count": len(session.events),
                    "state_keys": list(session.state.keys()) if session.state else [],
                    "last_update": session.last_update_time,
                    "recent_events": []
                }
                
                # Get recent events (last 5)
                for event in session.events[-5:]:
                    event_summary = {
                        "type": type(event).__name__,
                        "timestamp": getattr(event, 'timestamp', 'unknown'),
                    }
                    
                    # Extract content based on event type
                    if hasattr(event, 'content'):
                        if hasattr(event.content, 'parts'):
                            for part in event.content.parts:
                                if hasattr(part, 'text') and part.text:
                                    event_summary["text_snippet"] = part.text[:100] + "..." if len(part.text) > 100 else part.text
                                    break
                        elif hasattr(event.content, 'text'):
                            event_summary["text_snippet"] = event.content.text[:100] + "..." if len(event.content.text) > 100 else event.content.text
                    
                    session_info["recent_events"].append(event_summary)
                
                debug_info["session_investigation"]["session_list"].append(session_info)
                logger.info(f"Session {session.id}: {len(session.events)} events, state keys: {list(session.state.keys()) if session.state else 'none'}")
            
        except Exception as e:
            debug_info["session_investigation"]["list_error"] = str(e)
            logger.error(f"Error listing sessions: {e}")
        
        # Memory analysis - compare session data vs Graphiti search
        debug_info["memory_analysis"]["graphiti_vs_sessions"] = {
            "graphiti_facts_found": 0,
            "session_events_with_steven": 0,
            "session_state_with_steven": 0
        }
        
        # Quick Graphiti search for comparison
        try:
            from multi_tool_agent.agent import graphiti_client
            from graphiti_core.search.search_config_recipes import EDGE_HYBRID_SEARCH_RRF
            
            if graphiti_client:
                edge_config = EDGE_HYBRID_SEARCH_RRF.model_copy(deep=True)
                edge_config.limit = 10
                
                result = await graphiti_client._search(
                    query="Steven name user",
                    config=edge_config,
                    group_ids=[user_id]
                )
                
                facts = []
                if hasattr(result, 'edges'):
                    facts = [edge.fact.strip() for edge in result.edges if hasattr(edge, 'fact') and edge.fact]
                
                debug_info["memory_analysis"]["graphiti_vs_sessions"]["graphiti_facts_found"] = len(facts)
                debug_info["memory_analysis"]["graphiti_vs_sessions"]["sample_facts"] = facts[:3]
        
        except Exception as e:
            debug_info["memory_analysis"]["graphiti_error"] = str(e)
        
        # Search for Steven in session events and state
        steven_in_sessions = 0
        steven_in_state = 0
        
        if "session_list" in debug_info["session_investigation"]:
            for session_info in debug_info["session_investigation"]["session_list"]:
                # Check events for Steven mentions
                for event in session_info["recent_events"]:
                    if "text_snippet" in event and "steven" in event["text_snippet"].lower():
                        steven_in_sessions += 1
                
                # Check state for Steven mentions  
                for key in session_info["state_keys"]:
                    if "steven" in key.lower():
                        steven_in_state += 1
        
        debug_info["memory_analysis"]["graphiti_vs_sessions"]["session_events_with_steven"] = steven_in_sessions
        debug_info["memory_analysis"]["graphiti_vs_sessions"]["session_state_with_steven"] = steven_in_state
        
        # Summary
        debug_info["summary"] = {
            "user_id": user_id,
            "session_service_type": service_type,
            "total_sessions": debug_info["session_investigation"].get("sessions_found", 0),
            "steven_found_in": {
                "graphiti": debug_info["memory_analysis"]["graphiti_vs_sessions"]["graphiti_facts_found"] > 0,
                "session_events": steven_in_sessions > 0,
                "session_state": steven_in_state > 0
            },
            "diagnosis": "Session-based memory vs direct Graphiti search comparison"
        }
        
        return debug_info
        
    except Exception as e:
        logger.error(f"Session debug error: {e}")
        return {"error": str(e)}

@app.get("/api/memory/debug")
async def get_memory_debug(user_id: str = "user_102079780777749597961", limit: int = 50):
    """Comprehensive debug endpoint - all diagnostic approaches"""
    try:
        from multi_tool_agent.agent import graphiti_client, retrieve_info_from_memory
        from graphiti_core.search.search_config_recipes import EDGE_HYBRID_SEARCH_RRF, NODE_HYBRID_SEARCH_RRF
        
        if not graphiti_client:
            return {"error": "Graphiti client not available"}
        
        debug_info = {
            "diagnostic_approaches": {},
            "summary": {}
        }
        
        # APPROACH A: Test different user IDs
        logger.info("🔍 APPROACH A: Testing different user IDs")
        test_user_ids = [
            user_id,  # Current user
            "user123",  # Development fallback
            "bc_local_user",  # BC knowledge
            "",  # Empty group
        ]
        
        debug_info["diagnostic_approaches"]["A_user_ids"] = {}
        
        for test_user in test_user_ids:
            edge_config = EDGE_HYBRID_SEARCH_RRF.model_copy(deep=True)
            edge_config.limit = 20
            
            group_ids = [test_user] if test_user else []
            try:
                result = await graphiti_client._search(
                    query="Steven name user",
                    config=edge_config,
                    group_ids=group_ids
                )
                
                facts = []
                if hasattr(result, 'edges'):
                    facts = [edge.fact.strip() for edge in result.edges if hasattr(edge, 'fact') and edge.fact]
                
                debug_info["diagnostic_approaches"]["A_user_ids"][test_user or "empty"] = {
                    "facts_found": len(facts),
                    "sample_facts": facts[:5]
                }
                logger.info(f"User ID '{test_user or 'empty'}': Found {len(facts)} facts")
                
            except Exception as e:
                debug_info["diagnostic_approaches"]["A_user_ids"][test_user or "empty"] = {"error": str(e)}
        
        # APPROACH B: Test broader search scopes
        logger.info("🔍 APPROACH B: Testing broader search scopes")
        debug_info["diagnostic_approaches"]["B_search_scopes"] = {}
        
        scope_tests = [
            ("single_user", [user_id]),
            ("with_bc_knowledge", [user_id, "bc_local_user"]),
            ("all_users", [user_id, "user123", "bc_local_user"]),
            ("no_groups", []),  # Search everything
        ]
        
        for scope_name, group_ids in scope_tests:
            try:
                edge_config = EDGE_HYBRID_SEARCH_RRF.model_copy(deep=True)
                edge_config.limit = 30
                
                result = await graphiti_client._search(
                    query="Steven",
                    config=edge_config,
                    group_ids=group_ids
                )
                
                facts = []
                if hasattr(result, 'edges'):
                    facts = [edge.fact.strip() for edge in result.edges if hasattr(edge, 'fact') and edge.fact]
                
                debug_info["diagnostic_approaches"]["B_search_scopes"][scope_name] = {
                    "group_ids": group_ids,
                    "facts_found": len(facts),
                    "sample_facts": facts[:5]
                }
                logger.info(f"Scope '{scope_name}': Found {len(facts)} facts")
                
            except Exception as e:
                debug_info["diagnostic_approaches"]["B_search_scopes"][scope_name] = {"error": str(e)}
        
        # APPROACH C: Test agent's exact search method
        logger.info("🔍 APPROACH C: Testing agent's exact search method")
        debug_info["diagnostic_approaches"]["C_agent_method"] = {}
        
        try:
            # Create a mock tool context (simplified)
            class MockInvocationContext:
                def __init__(self):
                    self.user_id = user_id
                    self.session = type('Session', (), {'id': 'debug_session'})()
            
            class MockToolContext:
                def __init__(self):
                    self._invocation_context = MockInvocationContext()
            
            mock_context = MockToolContext()
            
            # Test queries that we know the agent uses successfully
            agent_test_queries = [
                "Do you know my name?",
                "Steven name user",
                "weather New West weekend"
            ]
            
            for query in agent_test_queries:
                try:
                    result = await retrieve_info_from_memory(query, mock_context)
                    debug_info["diagnostic_approaches"]["C_agent_method"][f"query_{query.replace(' ', '_')}"] = {
                        "query": query,
                        "agent_result": result
                    }
                    logger.info(f"Agent method query '{query}': {result.get('status', 'unknown')}")
                    
                except Exception as e:
                    debug_info["diagnostic_approaches"]["C_agent_method"][f"query_{query.replace(' ', '_')}"] = {
                        "query": query,
                        "error": str(e)
                    }
                    
        except Exception as e:
            debug_info["diagnostic_approaches"]["C_agent_method"] = {"error": f"Failed to test agent method: {str(e)}"}
        
        # APPROACH D: Raw data dump - search everything possible
        logger.info("🔍 APPROACH D: Raw data dump - search everything")
        debug_info["diagnostic_approaches"]["D_raw_dump"] = {}
        
        try:
            # Try very broad searches with high limits
            edge_config = EDGE_HYBRID_SEARCH_RRF.model_copy(deep=True)
            edge_config.limit = 100
            
            raw_searches = [
                ("all_groups_empty_query", [], ""),
                ("all_groups_single_char", [], "a"),
                ("user_only_empty", [user_id], ""),
                ("user_only_broad", [user_id], "user name family child parent"),
            ]
            
            total_unique_facts = set()
            
            for search_name, groups, query in raw_searches:
                try:
                    result = await graphiti_client._search(
                        query=query,
                        config=edge_config,
                        group_ids=groups
                    )
                    
                    facts = []
                    if hasattr(result, 'edges'):
                        facts = [edge.fact.strip() for edge in result.edges if hasattr(edge, 'fact') and edge.fact]
                    
                    total_unique_facts.update(facts)
                    
                    debug_info["diagnostic_approaches"]["D_raw_dump"][search_name] = {
                        "group_ids": groups,
                        "query": query,
                        "facts_found": len(facts),
                        "sample_facts": facts[:10]
                    }
                    logger.info(f"Raw dump '{search_name}': Found {len(facts)} facts")
                    
                except Exception as e:
                    debug_info["diagnostic_approaches"]["D_raw_dump"][search_name] = {"error": str(e)}
            
            debug_info["diagnostic_approaches"]["D_raw_dump"]["total_unique_facts"] = len(total_unique_facts)
            debug_info["diagnostic_approaches"]["D_raw_dump"]["sample_unique_facts"] = list(total_unique_facts)[:20]
            
        except Exception as e:
            debug_info["diagnostic_approaches"]["D_raw_dump"] = {"error": str(e)}
        
        # Overall summary
        debug_info["summary"] = {
            "target_user_id": user_id,
            "approaches_tested": ["A_user_ids", "B_search_scopes", "C_agent_method", "D_raw_dump"],
            "key_findings": "See diagnostic_approaches for detailed results"
        }
        
        return debug_info
        
    except Exception as e:
        logger.error(f"Comprehensive memory debug error: {e}")
        return {"error": str(e)}

@app.get("/api/memory/summary")
async def get_memory_summary(session_id: str = "default", req: Request = None):
    """Get formatted memory summary for the Memory Panel"""
    try:
        # Get authenticated user ID (optional for development, required in production)
        user_id = get_authenticated_user_id_optional(req) if req else None
        if not user_id:
            # Development mode: use the actual authenticated user from recent sessions
            # Check recent logs for user_102079780777749597961 (Steven)
            user_id = "user_102079780777749597961"  # Steven's actual user ID
            logger.warning(f"No authentication provided for memory summary, using known user ID: {user_id}")
        
        if not runner or not runner.session_service:
            return JSONResponse(content={
                "error": "Memory service not available",
                "data": get_mock_memory_data()
            }, status_code=503)
        
        # Try to get memory data from Graphiti
        try:
            # Import the agent to access graphiti_client and search configs
            from multi_tool_agent.agent import graphiti_client
            from graphiti_core.search.search_config_recipes import EDGE_HYBRID_SEARCH_RRF, NODE_HYBRID_SEARCH_RRF
            
            if not graphiti_client:
                logger.warning("Graphiti client not available, using mock data")
                return JSONResponse(content={
                    "source": "mock",
                    "data": get_mock_memory_data()
                })
            
            # Search for different types of memory using the same approach as retrieve_info_from_memory
            memory_summary = {
                "children": [],
                "preferences": [],
                "recentActivities": [],
                "storedFacts": []
            }
            
            # Configure search parameters (same as agent)
            edge_config = EDGE_HYBRID_SEARCH_RRF.model_copy(deep=True)
            edge_config.limit = 15
            
            node_config = NODE_HYBRID_SEARCH_RRF.model_copy(deep=True)
            node_config.limit = 12
            
            logger.info(f"🔍 Searching memory for user: {user_id}")
            
            # Use the same search approach as the agent's retrieve_info_from_memory
            # Test with a simple query first to match what the agent uses
            search_queries = [
                "Do you know my name?",  # Same query the agent used successfully
                "Steven name user",  # Direct name search
                "weather New West weekend",  # Recent conversation query
                "family parent child",  # Family info
                "user name identity",  # Personal identity search
                "live location address",  # Location info
                "",  # Empty query to get everything
            ]
            
            all_facts = []
            all_edges = []
            all_nodes = []
            
            # Execute all searches in parallel (like the agent does)
            # Search both personal user memory and shared BC knowledge
            search_groups = [user_id, "bc_local_user"]  # Same as agent
            
            # Also try broader searches to find personal data that might be under different group IDs
            possible_user_groups = [
                user_id,
                "user123",  # Development fallback
                "",  # No group (might catch older data)
            ]
            
            search_tasks = []
            for query in search_queries:
                # Edge search with multiple group IDs
                search_tasks.append(("edge", graphiti_client._search(query=query, config=edge_config, group_ids=search_groups)))
                # Node search with multiple group IDs
                search_tasks.append(("node", graphiti_client._search(query=query, config=node_config, group_ids=search_groups)))
                
                # Also try search with just user ID
                search_tasks.append(("edge_user", graphiti_client._search(query=query, config=edge_config, group_ids=[user_id])))
                search_tasks.append(("node_user", graphiti_client._search(query=query, config=node_config, group_ids=[user_id])))
                
                # Try broader search for personal queries to find Steven's data wherever it might be
                if query in ["Do you know my name?", "Steven name user", "user name identity"]:
                    for possible_group in possible_user_groups:
                        if possible_group:  # Skip empty string group for individual searches
                            search_tasks.append(("edge_broad", graphiti_client._search(query=query, config=edge_config, group_ids=[possible_group])))
                            search_tasks.append(("node_broad", graphiti_client._search(query=query, config=node_config, group_ids=[possible_group])))
                    
                    # Also try with no group filter at all for personal queries
                    search_tasks.append(("edge_all", graphiti_client._search(query=query, config=edge_config, group_ids=[])))
                    search_tasks.append(("node_all", graphiti_client._search(query=query, config=node_config, group_ids=[])))
            
            import asyncio
            search_results = await asyncio.gather(*[task[1] for task in search_tasks], return_exceptions=True)
            
            # Collect all results
            for i, result in enumerate(search_results):
                search_type = search_tasks[i][0]
                
                if isinstance(result, Exception):
                    logger.error(f"Search error ({search_type}): {result}")
                    continue
                    
                if search_type.startswith("edge") and hasattr(result, 'edges'):
                    all_edges.extend(result.edges)
                elif search_type.startswith("node") and hasattr(result, 'nodes'):
                    all_nodes.extend(result.nodes)
            
            # Process all edges for facts
            logger.info(f"Processing {len(all_edges)} edges and {len(all_nodes)} nodes from search results")
            for edge in all_edges:
                if hasattr(edge, 'fact') and edge.fact:
                    fact_text = edge.fact.strip()
                    if len(fact_text) > 10:
                        all_facts.append(fact_text)
                        logger.info(f"Found edge fact: {fact_text}")
            
            # Process all nodes
            for node in all_nodes:
                if hasattr(node, 'name') and node.name:
                    node_text = node.name.strip()
                    if hasattr(node, 'summary') and node.summary:
                        node_text = f"{node_text}: {node.summary}"
                    if len(node_text) > 10:
                        all_facts.append(node_text)
                        logger.info(f"Found node fact: {node_text}")
            
            # Remove duplicates
            unique_facts = []
            seen = set()
            for fact in all_facts:
                fact_lower = fact.lower()
                if fact_lower not in seen:
                    seen.add(fact_lower)
                    unique_facts.append(fact)
            
            # Process facts into categories
            processed_children = []
            processed_preferences = []
            processed_activities = []
            general_facts = []
            
            for fact in unique_facts:
                fact_lower = fact.lower()
                fact_added = False
                
                # Extract child information (only for personal child mentions)
                if any(keyword in fact_lower for keyword in ['my child', 'my son', 'my daughter', 'have a child', 'have a son', 'have a daughter', 'year old']):
                    child_info = extract_child_info_from_fact(fact)
                    if child_info and child_info not in processed_children:
                        processed_children.append(child_info)
                        fact_added = True
                        logger.info(f"Added to children: {child_info}")
                
                # Extract preferences (only for personal preferences)
                if not fact_added and any(keyword in fact_lower for keyword in ['i prefer', 'i like', 'my favorite', 'i enjoy', 'we prefer', 'we like']):
                    pref_info = extract_preference_from_text(fact)
                    if pref_info and pref_info not in processed_preferences:
                        processed_preferences.append(pref_info)
                        fact_added = True
                        logger.info(f"Added to preferences: {pref_info}")
                
                # Extract activities (only for personal registrations/activities)
                if not fact_added and any(keyword in fact_lower for keyword in ['registered for', 'signed up for', 'completed', 'my registration']):
                    activity_info = extract_activity_from_text(fact)
                    if activity_info and activity_info not in processed_activities:
                        processed_activities.append(activity_info)
                        fact_added = True
                        logger.info(f"Added to activities: {activity_info}")
                
                # Everything else goes to general facts (including all BC knowledge, Steven's name, etc.)
                if not fact_added:
                    if fact not in general_facts:
                        general_facts.append(fact)
                        logger.info(f"Added to general facts: {fact}")
            
            memory_summary["children"] = processed_children[:5]  # Limit results
            memory_summary["preferences"] = processed_preferences[:5]
            memory_summary["recentActivities"] = processed_activities[:5]
            memory_summary["storedFacts"] = general_facts[:10]
            
            logger.info(f"Memory summary retrieved for {user_id}: {len(memory_summary['children'])} children, {len(memory_summary['preferences'])} preferences, {len(memory_summary['storedFacts'])} facts")
            
            return JSONResponse(content={
                "source": "graphiti",
                "data": memory_summary,
                "session_id": session_id,
                "user_id": user_id
            })
            
        except Exception as e:
            logger.error(f"Error accessing Graphiti memory: {e}")
            return JSONResponse(content={
                "source": "mock_fallback",
                "data": get_mock_memory_data(),
                "error": f"Memory access error: {str(e)}"
            })
            
    except Exception as e:
        logger.error(f"Memory summary endpoint error: {e}")
        return JSONResponse(content={
            "error": f"Failed to get memory summary: {str(e)}",
            "data": get_mock_memory_data()
        }, status_code=500)

def get_mock_memory_data():
    """Fallback mock data when Graphiti is unavailable"""
    return {
        "children": [
            {"name": "Emma", "age": 5, "interests": ["swimming", "biking"]},
            {"name": "Liam", "age": 8, "interests": ["soccer", "art"]}
        ],
        "preferences": [
            {"type": "location", "value": "New Westminster", "strength": "strong"},
            {"type": "activity_type", "value": "biking", "strength": "medium"},
            {"type": "time_preference", "value": "weekends", "strength": "strong"}
        ],
        "recentActivities": [
            {"name": "Pedalheads Summer 2024", "status": "completed", "satisfaction": "loved_it"},
            {"name": "Community Swimming", "status": "interested", "date": "2025-06-01"}
        ],
        "storedFacts": [
            "Family lives in New Westminster",
            "Emma needs beginner biking program",
            "Looking for summer 2025 activities",
            "Budget conscious for multiple children"
        ]
    }

def extract_child_name(text: str) -> str:
    """Extract child name from memory text"""
    # Only extract names from text that clearly mentions children
    text_lower = text.lower()
    if not any(keyword in text_lower for keyword in ['child', 'son', 'daughter', 'kid', 'born', 'year old']):
        return ""
    
    import re
    patterns = [
        r'child named (\w+)',
        r'daughter named (\w+)',
        r'son named (\w+)', 
        r'have a (?:daughter|son|child) (?:named )?(\w+)',
        r'(\w+) is \d+ years old',
        r'my (?:daughter|son|child) (\w+)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            name = match.group(1)
            # Very strict filtering of non-names
            if (name.lower() not in ['child', 'son', 'daughter', 'kid', 'my', 'the', 'a', 'an', 'year', 'old', 'is', 'was', 'has', 'have']
                and len(name) > 1 
                and name.isalpha()
                and name[0].isupper()):
                return name.capitalize()
    return ""

def extract_child_info_from_fact(fact: str) -> dict:
    """Extract child information from a memory fact"""
    import re
    
    # Only process facts that clearly mention children
    fact_lower = fact.lower()
    if not any(keyword in fact_lower for keyword in ['child', 'son', 'daughter', 'kid', 'born', 'year old', 'grade']):
        return None
    
    # Look for child patterns
    child_info = {
        "name": "",
        "age": 0,
        "interests": []
    }
    
    # Extract name - be more specific to avoid false matches
    name_patterns = [
        r'child named (\w+)',
        r'daughter named (\w+)',
        r'son named (\w+)',
        r'(\w+) is \d+ years old',
        r'my (\w+) is \d+',
        r'(\w+) \(\d+ years old\)',
        r'have a (?:daughter|son|child) (?:named )?(\w+)',
    ]
    
    for pattern in name_patterns:
        match = re.search(pattern, fact, re.IGNORECASE)
        if match:
            name = match.group(1)
            # More strict filtering of non-names
            if (name.lower() not in ['child', 'son', 'daughter', 'kid', 'my', 'the', 'a', 'an', 'year', 'old', 'grade', 'school']
                and len(name) > 1 
                and name.isalpha()
                and name[0].isupper()):
                child_info["name"] = name.capitalize()
                break
    
    # Extract age
    age_patterns = [
        r'(\d+) years? old',
        r'age (\d+)',
        r'is (\d+) years',
        r'\((\d+) years old\)',
    ]
    
    for pattern in age_patterns:
        age_match = re.search(pattern, fact)
        if age_match:
            age = int(age_match.group(1))
            if 1 <= age <= 18:  # Reasonable child age range
                child_info["age"] = age
                break
    
    # Extract interests
    interests = []
    activity_words = ['swimming', 'biking', 'soccer', 'art', 'music', 'dance', 'hockey', 'tennis', 'basketball', 'gymnastics', 'piano', 'violin']
    for activity in activity_words:
        if activity in fact.lower():
            interests.append(activity)
    
    child_info["interests"] = interests
    
    # Only return if we found a valid name AND it's in a child context
    if (child_info["name"] and 
        any(keyword in fact_lower for keyword in ['child', 'son', 'daughter', 'kid', 'born', 'year old', 'grade'])):
        return child_info
    return None

def extract_age_from_text(text: str) -> int:
    """Extract age from memory text"""
    import re
    age_match = re.search(r'(\d+) years? old', text)
    if age_match:
        return int(age_match.group(1))
    
    age_match = re.search(r'age (\d+)', text)
    if age_match:
        return int(age_match.group(1))
    
    return 0

def extract_interests_from_text(text: str) -> list:
    """Extract interests from memory text"""
    interests = []
    text_lower = text.lower()
    
    common_activities = ['swimming', 'biking', 'soccer', 'art', 'music', 'dance', 'hockey', 'tennis', 'gymnastics']
    
    for activity in common_activities:
        if activity in text_lower:
            interests.append(activity)
    
    return interests

def extract_preference_from_text(text: str) -> dict:
    """Extract preference information from memory text"""
    text_lower = text.lower()
    
    # Location preferences
    if 'location' in text_lower or any(city in text_lower for city in ['vancouver', 'burnaby', 'richmond', 'surrey']):
        for city in ['new westminster', 'vancouver', 'burnaby', 'richmond', 'surrey']:
            if city in text_lower:
                return {
                    "type": "location",
                    "value": city.title(),
                    "strength": "strong" if 'prefer' in text_lower or 'live' in text_lower else "medium"
                }
    
    # Activity type preferences
    activity_types = ['swimming', 'biking', 'soccer', 'art', 'music']
    for activity in activity_types:
        if activity in text_lower and ('prefer' in text_lower or 'like' in text_lower):
            return {
                "type": "activity_type",
                "value": activity,
                "strength": "strong" if 'love' in text_lower else "medium"
            }
    
    return None

def extract_activity_from_text(text: str) -> dict:
    """Extract activity/registration information from memory text"""
    text_lower = text.lower()
    
    # Look for registration patterns
    if 'registration' in text_lower or 'registered' in text_lower:
        activity_name = ""
        status = "interested"
        satisfaction = None
        
        # Extract activity name
        if 'pedalheads' in text_lower:
            activity_name = "Pedalheads Program"
        elif 'swimming' in text_lower:
            activity_name = "Swimming Lessons"
        elif 'soccer' in text_lower:
            activity_name = "Soccer Program"
        
        # Extract status
        if 'completed' in text_lower or 'finished' in text_lower:
            status = "completed"
        elif 'registered' in text_lower:
            status = "registered"
        
        # Extract satisfaction
        if 'loved' in text_lower or 'great' in text_lower:
            satisfaction = "loved_it"
        elif 'okay' in text_lower:
            satisfaction = "okay"
        
        if activity_name:
            result = {
                "name": activity_name,
                "status": status
            }
            if satisfaction:
                result["satisfaction"] = satisfaction
            return result
    
    return None

# Calendar export endpoints
@app.post("/api/activity/export-calendar")
async def export_activity_to_calendar(request: dict):
    """Export activity registration/schedule to calendar (.ics file)"""
    try:
        activity_name = request.get('name', 'BC Activity')
        provider = request.get('provider', '')
        location = request.get('location', '')
        registration_date = request.get('registration_date')
        program_dates = request.get('program_dates', [])  # List of program session dates
        contact_info = request.get('contact_info', '')
        notes = request.get('notes', '')
        
        # Generate ICS content
        ics_content = generate_ics_file(
            activity_name=activity_name,
            provider=provider,
            location=location,
            registration_date=registration_date,
            program_dates=program_dates,
            contact_info=contact_info,
            notes=notes
        )
        
        # Return as downloadable file
        filename = f"{activity_name.replace(' ', '_')}_{provider.replace(' ', '_')}.ics"
        
        return Response(
            content=ics_content,
            media_type="text/calendar",
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Type": "text/calendar; charset=utf-8"
            }
        )
        
    except Exception as e:
        logger.error(f"Calendar export error: {e}")
        return JSONResponse(content={
            "error": f"Failed to export calendar: {str(e)}"
        }, status_code=500)

@app.get("/api/calendar/upcoming-deadlines")
async def get_upcoming_deadlines(session_id: str = "default"):
    """Get upcoming registration deadlines and important dates"""
    try:
        # This would eventually pull from stored activities and current BC timing
        current_time = datetime.now()
        
        # Generate BC activity registration deadlines based on season
        deadlines = generate_bc_activity_deadlines(current_time)
        
        return JSONResponse(content={
            "deadlines": deadlines,
            "generated_at": current_time.isoformat(),
            "session_id": session_id
        })
        
    except Exception as e:
        logger.error(f"Deadlines endpoint error: {e}")
        return JSONResponse(content={
            "error": f"Failed to get deadlines: {str(e)}",
            "deadlines": []
        }, status_code=500)

def generate_ics_file(activity_name: str, provider: str, location: str, 
                      registration_date: str = None, program_dates: list = None,
                      contact_info: str = "", notes: str = "") -> str:
    """Generate ICS calendar file content"""
    import uuid
    from datetime import datetime, timedelta
    
    ics_lines = [
        "BEGIN:VCALENDAR",
        "VERSION:2.0",
        "PRODID:-//BC Parent Activity Assistant//Activity Calendar//EN",
        "CALSCALE:GREGORIAN",
        "METHOD:PUBLISH"
    ]
    
    # Registration deadline event
    if registration_date:
        event_uid = str(uuid.uuid4())
        
        # Parse date (assume format like "2025-03-01" or "March 1, 2025")
        try:
            if "-" in registration_date:
                reg_date = datetime.strptime(registration_date, "%Y-%m-%d")
            else:
                reg_date = datetime.strptime(registration_date, "%B %d, %Y")
                
            # Create all-day event
            ics_lines.extend([
                "BEGIN:VEVENT",
                f"UID:{event_uid}",
                f"DTSTART;VALUE=DATE:{reg_date.strftime('%Y%m%d')}",
                f"DTEND;VALUE=DATE:{(reg_date + timedelta(days=1)).strftime('%Y%m%d')}",
                f"SUMMARY:📅 {activity_name} Registration Deadline - {provider}",
                f"DESCRIPTION:Registration deadline for {activity_name}",
                f"LOCATION:{location}",
                "STATUS:CONFIRMED",
                "TRANSP:TRANSPARENT",
                # Add reminder 7 days before
                "BEGIN:VALARM",
                "TRIGGER:-P7D",
                "ACTION:DISPLAY",
                f"DESCRIPTION:Registration for {activity_name} opens in 1 week!",
                "END:VALARM",
                # Add reminder 1 day before  
                "BEGIN:VALARM",
                "TRIGGER:-P1D", 
                "ACTION:DISPLAY",
                f"DESCRIPTION:Last day to register for {activity_name}!",
                "END:VALARM",
                "END:VEVENT"
            ])
        except ValueError:
            logger.warning(f"Could not parse registration date: {registration_date}")
    
    # Program session events
    if program_dates:
        for i, program_date in enumerate(program_dates):
            try:
                session_uid = str(uuid.uuid4())
                
                # Parse program date (assume format includes time)
                if isinstance(program_date, dict):
                    date_str = program_date.get('date')
                    time_str = program_date.get('time', '10:00')
                    duration = program_date.get('duration', 60)  # minutes
                else:
                    date_str = program_date
                    time_str = '10:00'
                    duration = 60
                
                # Combine date and time
                if "-" in date_str:
                    session_date = datetime.strptime(f"{date_str} {time_str}", "%Y-%m-%d %H:%M")
                else:
                    session_date = datetime.strptime(f"{date_str} {time_str}", "%B %d, %Y %H:%M")
                    
                end_time = session_date + timedelta(minutes=duration)
                
                ics_lines.extend([
                    "BEGIN:VEVENT",
                    f"UID:{session_uid}",
                    f"DTSTART:{session_date.strftime('%Y%m%dT%H%M%S')}",
                    f"DTEND:{end_time.strftime('%Y%m%dT%H%M%S')}",
                    f"SUMMARY:🏃 {activity_name} Session {i+1} - {provider}",
                    f"DESCRIPTION:{activity_name} program session\\n\\n{notes}",
                    f"LOCATION:{location}",
                    "STATUS:CONFIRMED",
                    # Add reminder 1 hour before
                    "BEGIN:VALARM",
                    "TRIGGER:-PT1H",
                    "ACTION:DISPLAY", 
                    f"DESCRIPTION:{activity_name} session starts in 1 hour!",
                    "END:VALARM",
                    "END:VEVENT"
                ])
            except (ValueError, KeyError) as e:
                logger.warning(f"Could not parse program date {program_date}: {e}")
    
    ics_lines.append("END:VCALENDAR")
    
    return "\r\n".join(ics_lines)

def generate_bc_activity_deadlines(current_time: datetime) -> list:
    """Generate upcoming BC activity registration deadlines"""
    current_month = current_time.month
    current_year = current_time.year
    
    deadlines = []
    
    # Winter/Spring deadlines (Jan-May)
    if current_month <= 5:
        deadlines.extend([
            {
                "title": "Summer Camp Early Bird Registration",
                "provider": "Various BC Providers",
                "deadline": f"{current_year}-02-15",
                "description": "Early bird pricing ends for most summer camps",
                "urgency": "medium" if current_month <= 2 else "high"
            },
            {
                "title": "Pedalheads Summer Program Registration",
                "provider": "Pedalheads", 
                "deadline": f"{current_year}-03-31",
                "description": "Popular summer biking programs fill up quickly",
                "urgency": "medium" if current_month <= 3 else "high"
            },
            {
                "title": "Community Center Summer Programs",
                "provider": "BC Community Centers",
                "deadline": f"{current_year}-04-30",
                "description": "Most community centers open summer registration",
                "urgency": "medium" if current_month <= 4 else "high"
            }
        ])
    
    # Summer deadlines (Jun-Aug)
    elif current_month <= 8:
        deadlines.extend([
            {
                "title": "Fall Sports Registration",
                "provider": "Various BC Sports Organizations",
                "deadline": f"{current_year}-08-15",
                "description": "Soccer, hockey, and other fall sports registration",
                "urgency": "medium" if current_month <= 7 else "high"
            },
            {
                "title": "September Activity Programs",
                "provider": "Community Centers",
                "deadline": f"{current_year}-08-31", 
                "description": "Fall session programs at community centers",
                "urgency": "medium" if current_month <= 8 else "high"
            }
        ])
    
    # Fall deadlines (Sep-Nov)
    else:
        deadlines.extend([
            {
                "title": f"{current_year + 1} Summer Camp Early Planning",
                "provider": "Various Providers",
                "deadline": f"{current_year}-12-31",
                "description": "Start researching next year's summer options",
                "urgency": "low"
            },
            {
                "title": "Winter Indoor Programs", 
                "provider": "Community Centers",
                "deadline": f"{current_year}-11-30",
                "description": "Swimming, gymnastics, and indoor sports",
                "urgency": "medium" if current_month <= 11 else "high"
            }
        ])
    
    # Filter to only future deadlines
    today = current_time.date()
    future_deadlines = []
    
    for deadline in deadlines:
        deadline_date = datetime.strptime(deadline["deadline"], "%Y-%m-%d").date()
        if deadline_date >= today:
            future_deadlines.append(deadline)
    
    return future_deadlines[:5]  # Return top 5 upcoming deadlines

if __name__ == "__main__":
    print("Enhanced ADK Server with Custom SSE Streaming")
    print(f"Session mapping: {len(session_id_map)} active sessions")
    print(f"SSE Events supported: {list(SSE_EVENT_TYPES.keys())}")
    
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=int(os.environ.get("PORT", 8080)))
