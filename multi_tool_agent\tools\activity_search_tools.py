import asyncio
import logging
import time
import hashlib
from typing import Dict, List, Optional, Any
from qdrant_client import AsyncQdrantClient, models
from datetime import datetime, timedelta

# Import ADK components
from google.adk.tools import ToolContext, LongRunningFunctionTool

from multi_tool_agent.config import AgentConfig
from .schemas import ActivityFilters

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Simple in-memory cache for frequently accessed queries
_search_cache = {}
_cache_ttl = AgentConfig.CACHE_TTL_SECONDS  # Configurable TTL

# No need for local embedding models - Qdrant handles this server-side!

async def _simple_search_fallback(query: str, qdrant_filter: Optional[models.Filter], limit: int = 10) -> List[Dict]:
    """Simple fallback search when embedding models are not available."""
    logger.info(f"🔍 Using simple search fallback for: {query}")

    qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
    try:
        # Simple text-based search using Qdrant's built-in capabilities
        search_response = await qdrant_client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=qdrant_filter,
            limit=limit,
            with_payload=True
        )

        results = [point.payload for point in search_response[0]]
        logger.info(f"✅ Simple search found {len(results)} results")
        return results

    except Exception as e:
        logger.error(f"❌ Simple search fallback failed: {e}")
        return []
    finally:
        await qdrant_client.close()

def _create_qdrant_filter(filters: Dict[str, Any]) -> Optional[models.Filter]:
    """Creates a Qdrant filter from a structured filter object."""
    must_conditions = []
    
    # Location filtering using city field
    if location := filters.get("location"):
        if "new west" in location.lower() or "new westminster" in location.lower():
            must_conditions.append(models.FieldCondition(key="city", match=models.MatchValue(value="New Westminster")))
        elif "burnaby" in location.lower():
            must_conditions.append(models.FieldCondition(key="city", match=models.MatchValue(value="Burnaby")))
    
    # Price Filtering
    if "max_price" in filters:
        try:
            max_price = float(filters["max_price"])
            must_conditions.append(models.FieldCondition(key="price_numeric", range=models.Range(lte=max_price)))
        except (ValueError, TypeError):
            logger.warning(f"Invalid max_price format: {filters['max_price']}")
    
    # Date Filtering
    if "date" in filters:
        try:
            # Assuming date is in 'YYYY-MM-DD' format
            target_date = filters["date"]
            # The activity must start on or before the target date AND end on or after it.
            must_conditions.append(models.FieldCondition(key="start_date", range=models.Range(lte=target_date)))
            must_conditions.append(models.FieldCondition(key="end_date", range=models.Range(gte=target_date)))
        except (ValueError, TypeError):
            logger.warning(f"Invalid date format: {filters['date']}")

    # Open Status Filtering
    if filters.get("is_open") is True:
        must_conditions.append(models.FieldCondition(key="is_open", match=models.MatchValue(value=True)))
    
    # Name Contains Filtering - Now enabled with text index on name field
    if name_filter := filters.get("name_contains"):
        must_conditions.append(models.FieldCondition(key="name", match=models.MatchText(text=name_filter)))

    # Age filtering - handle open-ended ranges properly
    if age := filters.get("age"):
        # Child must be at least the minimum age
        must_conditions.append(models.FieldCondition(key="min_age_years", range=models.Range(lte=age)))

        # For max age, include activities where max_age_years >= age OR max_age_years >= 99 (open-ended)
        # Note: We can't easily filter for null values in Qdrant, so we rely on the 99+ convention
        must_conditions.append(models.FieldCondition(key="max_age_years", range=models.Range(gte=age)))

    # Robustly handle the day_of_week filter
    if day_filters := filters.get("day_of_week"):
        # If the LLM provides a string, convert it to a list of one
        if isinstance(day_filters, str):
            day_filters_list = [day_filters]
        # If it's already a list, use it directly
        elif isinstance(day_filters, list):
            day_filters_list = day_filters
        # Otherwise, log a warning and skip this filter
        else:
            logger.warning(f"Unsupported type for 'day_of_week' filter: {type(day_filters)}. Skipping.")
            day_filters_list = None

        if day_filters_list:
            # Ensure all items in the list are strings, just in case
            safe_day_filters = [str(day).capitalize() for day in day_filters_list]
            must_conditions.append(
                models.FieldCondition(key="days_of_week_list", match=models.MatchAny(any=safe_day_filters))
            )

    return models.Filter(must=must_conditions) if must_conditions else None

async def _perform_advanced_search(query: str, qdrant_filter: Optional[models.Filter], limit: int = 10) -> List[Dict]:
    """
    Performs a state-of-the-art 3-stage reranking funnel with timeout protection:
    Stage 1: Ultra-fast hybrid search (dense + sparse) with binary quantization
    Stage 2: Fine-grained ColBERT reranking for precision
    Stage 3: Final scoring and ranking
    """
    # Performance monitoring
    start_time = time.time()

    try:
        # Add timeout protection to prevent hanging
        return await asyncio.wait_for(
            _perform_advanced_search_impl(query, qdrant_filter, limit, start_time),
            timeout=30.0  # 30 second timeout
        )
    except asyncio.TimeoutError:
        logger.error(f"⏰ Search timed out after 30s for query: {query}")
        # Fallback to simple search
        return await _simple_search_fallback(query, qdrant_filter, limit)
    except Exception as e:
        logger.error(f"❌ Advanced search failed: {e}")
        # Fallback to simple search
        return await _simple_search_fallback(query, qdrant_filter, limit)

async def _perform_advanced_search_impl(query: str, qdrant_filter: Optional[models.Filter], limit: int, start_time: float) -> List[Dict]:
    """Implementation of advanced search using Qdrant's server-side embeddings - NO local models needed!"""

    # Simple cache key generation
    cache_key = hashlib.md5(f"{query}_{str(qdrant_filter)}_{limit}".encode()).hexdigest()

    # Check cache first
    if cache_key in _search_cache:
        cached_result, cache_time = _search_cache[cache_key]
        if time.time() - cache_time < _cache_ttl:
            logger.info(f"⚡ Cache hit for query: {query[:50]}... (took {time.time() - start_time:.3f}s)")
            return cached_result

    qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
    try:
        # Use Qdrant's server-side embedding generation - much faster!
        # No need to download or load models locally
        logger.info(f"🔍 Server-side semantic search for: {query}")

        # Use the 'dense' vector for semantic search
        # Try multiple approaches to find the working API
        
        # Method 1: Try search with server-side embeddings using query_vector
        try:
            # Use Qdrant's server-side embedding generation with models.Document
            # Note: This requires the collection to have FastEmbed configured
            search_response = await qdrant_client.query_points(
                collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
                query=models.Document(
                    text=query,
                    model="sentence-transformers/all-mpnet-base-v2"  # Use 768-dim model to match collection
                ),
                using="dense",  # Specify which vector to use
                query_filter=qdrant_filter,
                limit=limit,
                with_payload=True
            )
            logger.info(f"✅ Using server-side embedding generation")
        except Exception as query_error:
            logger.warning(f"Server-side embedding search failed: {query_error}")
            
            # Method 2: Try search with actual vector (if we can generate embeddings)
            try:
                # Create a simple text-based vector approximation
                # This is a workaround - ideally we'd use a proper embedding model
                text_vector = [hash(word) % 1000 / 1000.0 for word in query.split()]
                # Pad or truncate to expected vector size (768 for BGE model)
                if len(text_vector) < 768:
                    text_vector.extend([0.0] * (768 - len(text_vector)))
                text_vector = text_vector[:768]
                
                search_response = await qdrant_client.search(
                    collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
                    query_vector=("dense", text_vector),
                    query_filter=qdrant_filter,
                    limit=limit,
                    with_payload=True
                )
                logger.info(f"✅ Using vector search with generated embeddings")
                
                # Handle different response formats
                if isinstance(search_response, list):
                    # Convert list to object with points attribute
                    class SearchResponse:
                        def __init__(self, points):
                            self.points = points
                    search_response = SearchResponse(search_response)
            except Exception as vector_error:
                logger.warning(f"Vector search failed: {vector_error}")
                
                # Method 3: Try just searching with filters (no vector)
                try:
                    if qdrant_filter:
                        search_response = await qdrant_client.scroll(
                            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
                            scroll_filter=qdrant_filter,
                            limit=limit,
                            with_payload=True
                        )
                        # Convert scroll response to search response format
                        class MockSearchResponse:
                            def __init__(self, points):
                                self.points = points
                        
                        search_response = MockSearchResponse(search_response[0])
                        logger.info(f"✅ Using filtered scroll search")
                    else:
                        raise Exception("No filter provided and vector search failed")
                except Exception as scroll_error:
                    logger.warning(f"Filtered scroll search failed: {scroll_error}")
                    # If all methods fail, fall back to simple search
                    return await _simple_search_fallback(query, qdrant_filter, limit)

        # Extract results - search_response should be defined by now
        if 'search_response' in locals():
            # Handle different response formats from different search methods
            if hasattr(search_response, 'points'):
                results = [point.payload for point in search_response.points]
            elif isinstance(search_response, list):
                # Handle list response format
                results = [point.payload if hasattr(point, 'payload') else point for point in search_response]
            else:
                logger.warning(f"Unexpected search response format: {type(search_response)}")
                results = []
        else:
            # This should not happen, but just in case
            logger.error("No search response available, falling back to simple search")
            return await _simple_search_fallback(query, qdrant_filter, limit)

        # Performance logging and caching
        total_time = time.time() - start_time
        logger.info(f"✅ 3-stage search completed: {len(results)} high-precision results in {total_time:.3f}s")

        # Cache the results for future queries
        _search_cache[cache_key] = (results, time.time())

        # Clean old cache entries (simple LRU-like behavior)
        if len(_search_cache) > 100:  # Keep cache size reasonable
            oldest_key = min(_search_cache.keys(), key=lambda k: _search_cache[k][1])
            del _search_cache[oldest_key]

        return results

    finally:
        await qdrant_client.close()

# --- Decomposed "Actuator" Tool ---
def _validate_activity_compatibility(first_activity: Dict, second_activity: Dict) -> bool:
    """
    Validates that activities can be taken together in a day.
    Rules based on proper level progression:
    1. A child can only take ONE level of a specific activity type (can't do Swimming Level 1 and Level 2)
    2. Different activity types are always compatible (Swimming Level 1 + Gymnastics Level 3 is OK)
    3. Non-leveled activities can be combined with anything
    4. Same exact class at same time is not allowed (duplicate booking)
    """
    def extract_activity_info(activity_name: str) -> tuple[str, int]:
        """Extract activity type and level from name like 'Swimming Level 02' -> ('Swimming', 2)"""
        import re
        # Look for 'Level XX' pattern
        level_match = re.search(r'Level\s+(\d+)', activity_name, re.IGNORECASE)
        level = int(level_match.group(1)) if level_match else 0
        
        # Extract activity type (everything before 'Level')
        if level > 0:
            activity_type = re.sub(r'\s+Level\s+\d+.*', '', activity_name, flags=re.IGNORECASE).strip()
        else:
            activity_type = activity_name.strip()
        
        return activity_type, level
    
    first_name = first_activity.get('name', '')
    second_name = second_activity.get('name', '')
    
    # Check if it's the exact same class at the exact same time (duplicate booking)
    if (first_name == second_name and 
        first_activity.get('start_time_iso') == second_activity.get('start_time_iso') and
        first_activity.get('start_date') == second_activity.get('start_date')):
        logger.info(f"❌ Duplicate booking: Same class at same time")
        return False
    
    first_type, first_level = extract_activity_info(first_name)
    second_type, second_level = extract_activity_info(second_name)
    
    # If both are leveled classes of the SAME activity type - NOT ALLOWED
    # A child can only take one level of a specific activity
    if (first_type.lower() == second_type.lower() and 
        first_level > 0 and second_level > 0):
        logger.info(f"❌ Can't take multiple levels of same activity: {first_name} and {second_name}")
        logger.info(f"   → A child must complete {first_type} Level {min(first_level, second_level)} before taking Level {max(first_level, second_level)}")
        return False
    
    # Different activity types are always compatible
    # e.g., Swimming Level 1 + Gymnastics Level 3 is perfectly fine
    if first_type.lower() != second_type.lower():
        if first_level > 0 and second_level > 0:
            logger.info(f"✅ Different activities at different levels: {first_name} + {second_name}")
        return True
    
    # Non-leveled activities are always compatible with everything
    return True

def _find_day_planning_activities(activities: List[Dict]) -> List[Dict]:
    """
    Finds activities that can be combined into a full day schedule.
    More flexible than back-to-back - allows 30+ minute gaps for meals, rest, travel.
    Returns activities grouped by day planning opportunities.
    Now supports cross-facility planning for better variety.
    """
    from datetime import datetime, timedelta

    # Group activities by date only (not facility) to allow cross-facility planning
    date_groups = {}
    for activity in activities:
        start_date = activity.get("start_date")
        if not start_date:
            continue

        if start_date not in date_groups:
            date_groups[start_date] = []
        date_groups[start_date].append(activity)

    back_to_back_results = []

    for date, date_activities in date_groups.items():
        # Sort activities by start time
        timed_activities = []
        for activity in date_activities:
            start_time_str = activity.get("start_time_iso")
            if start_time_str:
                try:
                    # Parse time (format: "HH:MM:SS")
                    start_time = datetime.strptime(start_time_str, "%H:%M:%S").time()
                    timed_activities.append((start_time, activity))
                except ValueError:
                    continue

        # Sort by start time
        timed_activities.sort(key=lambda x: x[0])

        # Find activity pairs with flexible gaps (5 minutes to 3 hours)
        for i in range(len(timed_activities)):
            for j in range(i + 1, len(timed_activities)):
                current_time, current_activity = timed_activities[i]
                next_time, next_activity = timed_activities[j]

                # Calculate time difference
                current_end_str = current_activity.get("end_time_iso")
                if current_end_str:
                    try:
                        current_end = datetime.strptime(current_end_str, "%H:%M:%S").time()
                        next_start = next_time

                        # Convert to datetime for calculation
                        base_date = datetime.today()
                        current_end_dt = datetime.combine(base_date, current_end)
                        next_start_dt = datetime.combine(base_date, next_start)

                        # Check if next class starts within reasonable time (5 min to 3 hours)
                        time_gap = (next_start_dt - current_end_dt).total_seconds() / 60

                        if 5 <= time_gap <= 180:  # 5 minutes to 3 hours gap
                            # Validate activity compatibility
                            if _validate_activity_compatibility(current_activity, next_activity):
                                # Determine gap type for better user experience
                                gap_type = "quick_transition" if time_gap <= 30 else "meal_break" if time_gap <= 90 else "extended_break"
                                
                                # Get facilities info for cross-facility planning
                                first_facility = current_activity.get("facility", "Unknown")
                                second_facility = next_activity.get("facility", "Unknown")
                                facilities = f"{first_facility}" if first_facility == second_facility else f"{first_facility} → {second_facility}"
                                
                                # Create day planning pair
                                day_pair = {
                                    "type": "day_planning_pair",
                                    "facilities": facilities,
                                    "cross_facility": first_facility != second_facility,
                                    "date": current_activity.get("start_date"),
                                    "first_class": current_activity,
                                    "second_class": next_activity,
                                    "time_gap_minutes": int(time_gap),
                                    "gap_type": gap_type,
                                    "total_duration": f"{current_activity.get('start_time_iso', '')} - {next_activity.get('end_time_iso', '')}",
                                    "compatibility_valid": True,
                                    "gap_description": _get_gap_description(time_gap)
                                }
                                back_to_back_results.append(day_pair)
                            else:
                                logger.info(f"⚠️ Skipping incompatible activities: {current_activity.get('name')} → {next_activity.get('name')}")

                    except ValueError:
                        continue

    return back_to_back_results

def _get_gap_description(time_gap_minutes: float) -> str:
    """
    Provides a user-friendly description of the gap between activities.
    """
    if time_gap_minutes <= 15:
        return "Quick transition - perfect for a bathroom break"
    elif time_gap_minutes <= 30:
        return "Short break - time for a snack and some rest"
    elif time_gap_minutes <= 60:
        return "Lunch break - perfect for a meal and relaxation"
    elif time_gap_minutes <= 90:
        return "Extended break - time for lunch and some play"
    elif time_gap_minutes <= 120:
        return "Long break - time for lunch, rest, and activities"
    else:
        return "Very long break - plenty of time for meals, rest, and other activities"

async def raw_activity_search(
    query: str,
    filters: Dict[str, Any],
    tool_context: ToolContext,
    find_back_to_back: bool = False,
) -> Dict[str, Any]:
    """
    Performs an advanced search for kids' activities and returns the raw,
    unstructured JSON data for another agent to process.
    """
    # This explicit validation is robust and ensures we're working with a typed object.
    try:
        validated_filters = ActivityFilters.model_validate(filters or {})
        filters_dict = validated_filters.model_dump(exclude_none=True)
    except Exception as e:
        logger.warning(f"Could not validate filters: {e}. Proceeding without them.")
        filters_dict = {}

    logger.info(f"🧠 Running RAW search. Query: '{query}', Filters: {filters_dict}, Back-to-back: {find_back_to_back}")
    try:
        final_limit = 100 if find_back_to_back else 50
        qdrant_filter = _create_qdrant_filter(filters_dict)
        activities = await _perform_advanced_search(query, qdrant_filter, limit=final_limit)

        if not activities:
            return {"status": "success", "results": []}

        # If back-to-back search is requested, find day planning activities
        if find_back_to_back:
            back_to_back_pairs = _find_day_planning_activities(activities)
            logger.info(f"🔗 Found {len(back_to_back_pairs)} day planning opportunities")

            # Limit to top 8 pairs to prevent token overflow
            limited_pairs = back_to_back_pairs[:8]
            limited_activities = activities[:20]  # Also limit individual activities

            # Return both individual activities and back-to-back pairs
            return {
                "status": "success",
                "results": limited_activities,
                "back_to_back_pairs": limited_pairs,
                "search_type": "back_to_back",
                "total_pairs_found": len(back_to_back_pairs)
            }

        # Return the raw list of activity dictionaries for the Synthesizer agent
        return {"status": "success", "results": activities}

    except Exception as e:
        logger.error(f"Error in raw_activity_search: {e}", exc_info=True)
        return {"status": "error", "message": "An error occurred during the search."}

# --- Human-in-the-Loop Confirmation Tool ---
def get_user_confirmation(plan: str, tool_context: ToolContext) -> str:
    """
    Presents an execution plan to the user and asks for their confirmation to proceed.
    This is a long-running tool that will pause until the user responds 'yes' or 'no'.
    """
    # This tells the ADK framework not to call an LLM to summarize this tool's output.
    # The 'plan' is meant to be displayed directly in the UI.
    tool_context.actions.skip_summarization = True
    # The return value is None because the framework will pause and wait for the
    # user to send back the confirmation in a separate turn.
    return None

# The ADK framework needs the function to be wrapped in a FunctionTool or LongRunningFunctionTool class.
user_confirmation_tool = LongRunningFunctionTool(func=get_user_confirmation)
