"""
Intelligent Activity Tools - Token-Efficient Architecture
Uses smart tools to offload heavy processing from LLM to save tokens and costs.
"""

import logging
import time
import hashlib
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from google.adk.tools import ToolContext

logger = logging.getLogger(__name__)

# Import existing search infrastructure
from .activity_search_tools import _perform_advanced_search, _simple_search_fallback, _create_qdrant_filter, ActivityFilters

# Simple response cache for API optimization (10 RPM limit)
_response_cache = {}
_cache_ttl = 1800  # 30 minutes

async def find_multi_age_activities(
    min_age: int,
    max_age: int,
    location: str = "New Westminster",
    query: str = "family activities",
    tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """
    Find activities that work for multiple ages (e.g., 3-year-old and 6-year-old together).

    This searches for activities where the age range overlaps with the requested age span.
    For example, if looking for activities for ages 3-6, it will find activities like:
    - Ages 3-5 (works for 3-year-old)
    - Ages 4-8 (works for both 3 and 6-year-old)
    - Ages 2-7 (works for both)

    Args:
        min_age: Youngest child's age
        max_age: Oldest child's age
        location: City to search in
        query: Search query for activity types
        tool_context: ADK tool context

    Returns:
        Dictionary with activities suitable for the age range
    """
    try:
        logger.info(f"🔍 Searching for multi-age activities (ages {min_age}-{max_age}) in {location}")

        # Create filters for activities that overlap with the requested age range
        # An activity is suitable if:
        # - Its min_age <= max_age (activity doesn't start too late)
        # - Its max_age >= min_age (activity doesn't end too early)
        filters = {
            "location": location
        }

        # Create a custom Qdrant filter for age range overlap
        from qdrant_client import models

        must_conditions = []

        # Location filtering
        if "new west" in location.lower() or "new westminster" in location.lower():
            must_conditions.append(models.FieldCondition(key="city", match=models.MatchValue(value="New Westminster")))
        elif "burnaby" in location.lower():
            must_conditions.append(models.FieldCondition(key="city", match=models.MatchValue(value="Burnaby")))

        # Age range overlap logic:
        # Activity min_age <= our max_age (activity doesn't start too late)
        must_conditions.append(models.FieldCondition(key="min_age_years", range=models.Range(lte=max_age)))

        # Activity max_age >= our min_age (activity doesn't end too early)
        must_conditions.append(models.FieldCondition(key="max_age_years", range=models.Range(gte=min_age)))

        qdrant_filter = models.Filter(must=must_conditions)

        # Perform the search
        activities = await _perform_advanced_search(query, qdrant_filter, limit=50)

        if not activities:
            return {
                "status": "success",
                "message": f"No activities found for ages {min_age}-{max_age} in {location}",
                "activities": [],
                "total_found": 0
            }

        # Categorize activities by how well they fit the age range
        perfect_fit = []  # Activities that exactly match or contain the full range
        partial_fit = []  # Activities that work for some but not all ages

        for activity in activities:
            activity_min = activity.get("min_age_years", 0)
            activity_max = activity.get("max_age_years", 99)

            # Perfect fit: activity range contains our entire range
            if activity_min <= min_age and activity_max >= max_age:
                perfect_fit.append(activity)
            else:
                partial_fit.append(activity)

        logger.info(f"✅ Found {len(perfect_fit)} perfect fit activities and {len(partial_fit)} partial fit activities")

        return {
            "status": "success",
            "message": f"Found {len(activities)} activities for ages {min_age}-{max_age} in {location}",
            "perfect_fit": perfect_fit,
            "partial_fit": partial_fit,
            "total_found": len(activities),
            "search_details": {
                "min_age": min_age,
                "max_age": max_age,
                "location": location,
                "query": query
            }
        }

    except Exception as e:
        logger.error(f"❌ Multi-age search failed: {e}")
        return {
            "status": "error",
            "message": f"Search failed: {str(e)}",
            "activities": [],
            "total_found": 0
        }

async def find_concurrent_activities(
    children_ages: List[int],
    location: str = "New Westminster",
    time_gap_minutes: int = 30,
    query: str = "activities",
    tool_context: Optional[ToolContext] = None
) -> Dict[str, Any]:
    """
    Find activities for multiple children that can be scheduled concurrently.

    This searches for:
    1. Same activities that work for all children (perfect overlap)
    2. Different activities at the same time or within time gap
    3. Back-to-back scheduling options for parents with multiple children

    Args:
        children_ages: List of children's ages (e.g., [3, 6, 8])
        location: City to search in
        time_gap_minutes: Maximum time gap between activities (default 30 minutes)
        query: Search query for activity types
        tool_context: ADK tool context

    Returns:
        Dictionary with concurrent scheduling options
    """
    try:
        logger.info(f"🔍 Finding concurrent activities for ages {children_ages} in {location}")

        if not children_ages or len(children_ages) < 2:
            return {
                "status": "error",
                "message": "Need at least 2 children ages for concurrent scheduling",
                "options": []
            }

        min_age = min(children_ages)
        max_age = max(children_ages)

        # Strategy 1: Find activities that work for all children together
        same_activity_options = await find_multi_age_activities(
            min_age=min_age,
            max_age=max_age,
            location=location,
            query=query,
            tool_context=tool_context
        )

        # Strategy 2: Find age-specific activities that can be scheduled concurrently
        concurrent_options = []

        # Get activities for each child individually
        individual_activities = {}
        for age in children_ages:
            filters = {"age": age, "location": location}
            qdrant_filter = _create_qdrant_filter(filters)
            activities = await _perform_advanced_search(query, qdrant_filter, limit=100)
            individual_activities[age] = activities

        # Find time-based matches
        concurrent_options = _find_time_based_matches(individual_activities, time_gap_minutes)

        # Strategy 3: Find facility-based options (same location, different times)
        facility_options = _find_facility_based_options(individual_activities, time_gap_minutes)

        return {
            "status": "success",
            "message": f"Found concurrent scheduling options for {len(children_ages)} children",
            "children_ages": children_ages,
            "time_gap_minutes": time_gap_minutes,
            "same_activity_options": {
                "perfect_fit": same_activity_options.get("perfect_fit", []),
                "partial_fit": same_activity_options.get("partial_fit", [])
            },
            "concurrent_different_activities": concurrent_options,
            "facility_based_options": facility_options,
            "total_options": len(same_activity_options.get("perfect_fit", [])) +
                           len(same_activity_options.get("partial_fit", [])) +
                           len(concurrent_options) +
                           len(facility_options)
        }

    except Exception as e:
        logger.error(f"❌ Concurrent activities search failed: {e}")
        return {
            "status": "error",
            "message": f"Search failed: {str(e)}",
            "options": []
        }

def _find_time_based_matches(individual_activities: Dict[int, List[Dict]], time_gap_minutes: int) -> List[Dict]:
    """Find activities that can be scheduled at the same time or within time gap."""
    from datetime import datetime, timedelta

    matches = []
    ages = list(individual_activities.keys())

    # Compare activities between different ages
    for i, age1 in enumerate(ages):
        for j, age2 in enumerate(ages[i+1:], i+1):
            activities1 = individual_activities[age1]
            activities2 = individual_activities[age2]

            for act1 in activities1:
                for act2 in activities2:
                    # Check if activities can be scheduled concurrently
                    time_match = _check_time_compatibility(act1, act2, time_gap_minutes)
                    if time_match:
                        matches.append({
                            "type": "concurrent_different",
                            "child1_age": age1,
                            "child1_activity": {
                                "name": act1.get("name"),
                                "facility": act1.get("facility"),
                                "start_time": act1.get("start_time_iso"),
                                "end_time": act1.get("end_time_iso"),
                                "date": act1.get("start_date"),
                                "price": act1.get("price_numeric"),
                                "activity_url": act1.get("activity_url")
                            },
                            "child2_age": age2,
                            "child2_activity": {
                                "name": act2.get("name"),
                                "facility": act2.get("facility"),
                                "start_time": act2.get("start_time_iso"),
                                "end_time": act2.get("end_time_iso"),
                                "date": act2.get("start_date"),
                                "price": act2.get("price_numeric"),
                                "activity_url": act2.get("activity_url")
                            },
                            "time_relationship": time_match["relationship"],
                            "time_gap_minutes": time_match["gap_minutes"],
                            "logistics": time_match["logistics"]
                        })

    return matches[:20]  # Limit to top 20 matches

def _find_facility_based_options(individual_activities: Dict[int, List[Dict]], time_gap_minutes: int) -> List[Dict]:
    """Find activities at the same facility for easier logistics."""
    facility_groups = {}

    # Group activities by facility
    for age, activities in individual_activities.items():
        for activity in activities:
            facility = activity.get("facility", "Unknown")
            if facility not in facility_groups:
                facility_groups[facility] = {}
            if age not in facility_groups[facility]:
                facility_groups[facility][age] = []
            facility_groups[facility][age].append(activity)

    facility_options = []

    # Find facilities with activities for multiple ages
    for facility, age_activities in facility_groups.items():
        if len(age_activities) >= 2:  # At least 2 different ages
            facility_options.append({
                "facility": facility,
                "ages_available": list(age_activities.keys()),
                "total_activities": sum(len(acts) for acts in age_activities.values()),
                "sample_activities": {
                    age: acts[:3] for age, acts in age_activities.items()  # Sample 3 per age
                }
            })

    return facility_options[:10]  # Limit to top 10 facilities

def _check_time_compatibility(act1: Dict, act2: Dict, max_gap_minutes: int) -> Optional[Dict]:
    """Check if two activities can be scheduled concurrently."""
    try:
        from datetime import datetime, timedelta

        # Get time information
        start1 = act1.get("start_time_iso")
        end1 = act1.get("end_time_iso")
        start2 = act2.get("start_time_iso")
        end2 = act2.get("end_time_iso")

        if not all([start1, end1, start2, end2]):
            return None

        # Parse times (assuming format like "09:00" or "09:00:00")
        def parse_time(time_str):
            if ":" in time_str:
                parts = time_str.split(":")
                return int(parts[0]) * 60 + int(parts[1])  # Convert to minutes
            return None

        start1_min = parse_time(start1)
        end1_min = parse_time(end1)
        start2_min = parse_time(start2)
        end2_min = parse_time(end2)

        if None in [start1_min, end1_min, start2_min, end2_min]:
            return None

        # Check different time relationships

        # Same time (overlapping)
        if (start1_min <= start2_min < end1_min) or (start2_min <= start1_min < end2_min):
            return {
                "relationship": "overlapping",
                "gap_minutes": 0,
                "logistics": "Parent needs to be at both locations simultaneously - requires coordination"
            }

        # Back-to-back (activity 1 ends, activity 2 starts)
        gap1 = start2_min - end1_min  # Gap if act1 ends before act2 starts
        gap2 = start1_min - end2_min  # Gap if act2 ends before act1 starts

        if 0 <= gap1 <= max_gap_minutes:
            return {
                "relationship": "sequential",
                "gap_minutes": gap1,
                "logistics": f"Activity 1 ends, then {gap1} minute gap before Activity 2 starts"
            }

        if 0 <= gap2 <= max_gap_minutes:
            return {
                "relationship": "sequential",
                "gap_minutes": gap2,
                "logistics": f"Activity 2 ends, then {gap2} minute gap before Activity 1 starts"
            }

        return None

    except Exception as e:
        logger.warning(f"Time compatibility check failed: {e}")
        return None

def _get_cache_key(query: str, filters: Dict[str, Any], extra: str = "") -> str:
    """Generate cache key for responses."""
    cache_input = f"{query.lower().strip()}|{str(sorted(filters.items()))}|{extra}"
    return hashlib.md5(cache_input.encode()).hexdigest()

def _get_cached_response(cache_key: str) -> Optional[Any]:
    """Get cached response if available and not expired."""
    if cache_key in _response_cache:
        response, timestamp = _response_cache[cache_key]
        if time.time() - timestamp < _cache_ttl:
            logger.info(f"🎯 Cache HIT for key: {cache_key[:8]}...")
            return response
        else:
            del _response_cache[cache_key]
    return None

def _cache_response(cache_key: str, response: Any) -> None:
    """Cache a response."""
    _response_cache[cache_key] = (response, time.time())
    logger.info(f"💾 Cached response for key: {cache_key[:8]}...")

class ActivityAnalyzer:
    """Smart analyzer that processes activity data without LLM involvement."""
    
    @staticmethod
    def find_patterns(activities: List[Dict], pattern_type: str) -> Dict[str, Any]:
        """
        Analyzes activities for specific patterns without sending data to LLM.
        Returns structured insights that LLM can easily interpret.
        """
        if pattern_type == "back_to_back":
            return ActivityAnalyzer._analyze_back_to_back(activities)
        elif pattern_type == "schedule_conflicts":
            return ActivityAnalyzer._analyze_conflicts(activities)
        elif pattern_type == "progression_paths":
            return ActivityAnalyzer._analyze_progressions(activities)
        elif pattern_type == "cost_optimization":
            return ActivityAnalyzer._analyze_costs(activities)
        else:
            return {"error": f"Unknown pattern type: {pattern_type}"}
    
    @staticmethod
    def _analyze_back_to_back(activities: List[Dict]) -> Dict[str, Any]:
        """Find day planning opportunities with flexible gaps for better user experience."""
        # Import the enhanced day planning function
        from .activity_search_tools import _find_day_planning_activities
        
        # Use the enhanced day planning logic
        day_planning_pairs = _find_day_planning_activities(activities)
        
        # Transform to analyzer format
        opportunities = []
        for pair in day_planning_pairs:
            first_class = pair.get("first_class", {})
            second_class = pair.get("second_class", {})
            
            opportunities.append({
                "facilities": pair.get("facilities"),  # Changed from "facility" to "facilities"
                "cross_facility": pair.get("cross_facility", False),
                "date": pair.get("date"),
                "gap_minutes": pair.get("time_gap_minutes"),
                "gap_type": pair.get("gap_type"),
                "gap_description": pair.get("gap_description"),
                "first_class": {
                    "name": first_class.get("name"),
                    "facility": first_class.get("facility"),
                    "time": f"{first_class.get('start_time_iso')} - {first_class.get('end_time_iso')}",
                    "price": first_class.get("price_numeric", 0),
                    "registration_url": first_class.get("activity_url", "")
                },
                "second_class": {
                    "name": second_class.get("name"),
                    "facility": second_class.get("facility"),
                    "time": f"{second_class.get('start_time_iso')} - {second_class.get('end_time_iso')}",
                    "price": second_class.get("price_numeric", 0),
                    "registration_url": second_class.get("activity_url", "")
                },
                "total_price": (first_class.get("price_numeric", 0) + second_class.get("price_numeric", 0)),
                "total_duration": pair.get("total_duration"),
                "compatibility_valid": pair.get("compatibility_valid", True)
            })
        
        return {
            "pattern_type": "day_planning",
            "total_opportunities": len(opportunities),
            "opportunities": opportunities[:8],  # Limit for token efficiency
            "summary": f"Found {len(opportunities)} day planning opportunities with flexible gaps (5min-3hrs) for better scheduling"
        }
    
    @staticmethod
    def _analyze_conflicts(activities: List[Dict]) -> Dict[str, Any]:
        """Analyze schedule conflicts."""
        conflicts = []
        # Implementation for conflict detection
        return {
            "pattern_type": "schedule_conflicts",
            "conflicts": conflicts,
            "summary": f"Found {len(conflicts)} potential schedule conflicts"
        }
    
    @staticmethod
    def _analyze_progressions(activities: List[Dict]) -> Dict[str, Any]:
        """Analyze skill progression paths."""
        progressions = {}
        # Group by activity type and analyze levels
        for activity in activities:
            name = activity.get("name", "")
            if "level" in name.lower():
                # Extract progression info
                pass
        
        return {
            "pattern_type": "progression_paths",
            "progressions": progressions,
            "summary": "Skill progression analysis"
        }
    
    @staticmethod
    def _analyze_costs(activities: List[Dict]) -> Dict[str, Any]:
        """Analyze cost optimization opportunities."""
        cost_analysis = {
            "cheapest_options": sorted(activities, key=lambda x: x.get("price_numeric", float('inf')))[:5],
            "price_ranges": {},
            "bulk_discounts": []
        }
        
        return {
            "pattern_type": "cost_optimization",
            "analysis": cost_analysis,
            "summary": "Cost optimization analysis"
        }

# Smart Tools that use ActivityAnalyzer

async def discover_activity_patterns(
    query: str,
    filters: Dict[str, Any],
    pattern_types: List[str],
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Intelligent pattern discovery tool that processes data efficiently.
    
    Args:
        query: Search query for activities
        filters: Activity filters (age, location, etc.)
        pattern_types: List of patterns to discover (back_to_back, conflicts, progressions, costs)
    
    Returns:
        Structured insights without heavy token usage
    """
    try:
        # Validate filters
        validated_filters = ActivityFilters.model_validate(filters or {})
        filters_dict = validated_filters.model_dump(exclude_none=True)
        
        logger.info(f"🔍 Discovering patterns: {pattern_types} for query: '{query}'")
        
        # Get activities using Qdrant's server-side embeddings
        qdrant_filter = _create_qdrant_filter(filters_dict)
        activities = await _perform_advanced_search(query, qdrant_filter, limit=50)
        
        if not activities:
            return {
                "status": "success",
                "message": "No activities found matching your criteria",
                "patterns": {}
            }
        
        # Analyze patterns efficiently (no LLM involved)
        patterns = {}
        for pattern_type in pattern_types:
            patterns[pattern_type] = ActivityAnalyzer.find_patterns(activities, pattern_type)
        
        # Return concise, structured results
        return {
            "status": "success",
            "total_activities_analyzed": len(activities),
            "patterns": patterns,
            "query_info": {
                "search_query": query,
                "filters_applied": filters_dict,
                "patterns_requested": pattern_types
            }
        }
        
    except Exception as e:
        logger.error(f"Error in discover_activity_patterns: {e}", exc_info=True)
        return {"status": "error", "message": "An error occurred during pattern discovery"}

async def get_activity_summary(
    query: str,
    filters: Dict[str, Any],
    summary_type: str,
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Get concise activity summary without sending full data to LLM.
    
    Args:
        query: Search query
        filters: Activity filters
        summary_type: Type of summary (quick, detailed, comparison)
    
    Returns:
        Structured summary ready for LLM interpretation
    """
    try:
        validated_filters = ActivityFilters.model_validate(filters or {})
        filters_dict = validated_filters.model_dump(exclude_none=True)
        
        logger.info(f"📊 Generating {summary_type} summary for: '{query}'")
        
        # Get activities using Qdrant's server-side embeddings
        qdrant_filter = _create_qdrant_filter(filters_dict)
        limit = 20 if summary_type == "quick" else 40
        activities = await _perform_advanced_search(query, qdrant_filter, limit=limit)
        
        if not activities:
            return {
                "status": "success",
                "summary_type": summary_type,
                "message": "No activities found",
                "stats": {"total_found": 0}
            }
        
        # Generate summary statistics (no LLM needed)
        facilities = set(a.get("facility") for a in activities if a.get("facility"))
        categories = set(a.get("category") for a in activities if a.get("category"))
        price_range = {
            "min": min((a.get("price_numeric", 0) for a in activities if a.get("price_numeric")), default=0),
            "max": max((a.get("price_numeric", 0) for a in activities if a.get("price_numeric")), default=0),
            "average": sum(a.get("price_numeric", 0) for a in activities) / len(activities) if activities else 0
        }
        
        # Sample activities for LLM (much smaller payload)
        sample_activities = activities[:5] if summary_type == "quick" else activities[:10]
        
        return {
            "status": "success",
            "summary_type": summary_type,
            "stats": {
                "total_found": len(activities),
                "facilities_count": len(facilities),
                "categories_count": len(categories),
                "price_range": price_range
            },
            "facilities": list(facilities),
            "categories": list(categories),
            "sample_activities": [
                {
                    "name": a.get("name"),
                    "facility": a.get("facility"),
                    "time": f"{a.get('start_time_iso')} - {a.get('end_time_iso')}",
                    "date": a.get("start_date"),
                    "price": a.get("price_numeric"),
                    "age_range": a.get("display_age")
                }
                for a in sample_activities
            ]
        }
        
    except Exception as e:
        logger.error(f"Error in get_activity_summary: {e}", exc_info=True)
        return {"status": "error", "message": "An error occurred generating summary"}

async def check_activity_availability(
    activity_names: List[str],
    preferred_times: List[str],
    filters: Dict[str, Any],
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Check availability for specific activities at preferred times.
    Efficient tool that returns yes/no answers without heavy data.
    """
    try:
        validated_filters = ActivityFilters.model_validate(filters or {})
        filters_dict = validated_filters.model_dump(exclude_none=True)
        
        availability_results = {}
        
        for activity_name in activity_names:
            # Search for specific activity using Qdrant's server-side embeddings
            qdrant_filter = _create_qdrant_filter(filters_dict)
            activities = await _perform_advanced_search(activity_name, qdrant_filter, limit=20)
            
            # Check time preferences
            available_times = []
            for activity in activities:
                start_time = activity.get("start_time_iso")
                if start_time:
                    available_times.append({
                        "time": start_time,
                        "date": activity.get("start_date"),
                        "facility": activity.get("facility"),
                        "is_open": activity.get("is_open", False)
                    })
            
            # Match with preferred times
            matches = []
            for pref_time in preferred_times:
                for avail in available_times:
                    if pref_time in avail["time"] and avail["is_open"]:
                        matches.append(avail)
            
            availability_results[activity_name] = {
                "total_found": len(activities),
                "available_at_preferred_times": len(matches),
                "matches": matches[:3],  # Limit results
                "has_availability": len(matches) > 0
            }
        
        return {
            "status": "success",
            "availability": availability_results,
            "summary": f"Checked availability for {len(activity_names)} activities"
        }
        
    except Exception as e:
        logger.error(f"Error in check_activity_availability: {e}", exc_info=True)
        return {"status": "error", "message": "An error occurred checking availability"}

async def plan_full_day_schedule(
    query: str,
    filters: Dict[str, Any],
    preferred_gaps: List[str],  # ["quick", "meal", "extended"]
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Intelligent day planning tool that can mix and match activities from different sources.
    Uses agent's decision-making power to create optimal schedules.
    
    Args:
        query: What kind of activities to plan for
        filters: Activity filters (age, location, etc.)
        preferred_gaps: Preferred gap types between activities
    
    Returns:
        Comprehensive day schedule with mixed sources (DB + web search if needed)
    """
    try:
        validated_filters = ActivityFilters.model_validate(filters or {})
        filters_dict = validated_filters.model_dump(exclude_none=True)
        
        logger.info(f"🎯 Planning full day schedule for: '{query}' with gaps: {preferred_gaps}")
        
        # Step 1: Get base activities from database
        qdrant_filter = _create_qdrant_filter(filters_dict)
        db_activities = await _perform_advanced_search(query, qdrant_filter, limit=50)
        
        # Step 2: Use enhanced day planning
        from .activity_search_tools import _find_day_planning_activities
        day_planning_pairs = _find_day_planning_activities(db_activities)
        
        # Step 3: Filter by preferred gaps
        filtered_pairs = []
        for pair in day_planning_pairs:
            gap_type = pair.get("gap_type", "")
            if not preferred_gaps or gap_type in preferred_gaps:
                filtered_pairs.append(pair)
        
        # Step 4: Intelligent gap analysis
        schedule_analysis = {
            "morning_slots": [],
            "afternoon_slots": [],
            "evening_slots": [],
            "gaps_identified": [],
            "enhancement_suggestions": []
        }
        
        # Analyze time slots
        for pair in filtered_pairs:
            first_class = pair.get("first_class", {})
            start_time = first_class.get("start_time_iso", "")
            
            if start_time:
                hour = int(start_time.split(":")[0]) if ":" in start_time else 12
                if hour < 12:
                    schedule_analysis["morning_slots"].append(pair)
                elif hour < 17:
                    schedule_analysis["afternoon_slots"].append(pair)
                else:
                    schedule_analysis["evening_slots"].append(pair)
        
        # Step 5: Identify gaps that could be filled
        all_activities = db_activities[:20]  # Sample for gap analysis
        time_gaps = []
        
        for pair in filtered_pairs:
            gap_minutes = pair.get("time_gap_minutes", 0)
            if gap_minutes > 60:  # Long gaps that could be filled
                time_gaps.append({
                    "gap_duration": gap_minutes,
                    "gap_type": pair.get("gap_type"),
                    "between_activities": f"{pair.get('first_class', {}).get('name')} and {pair.get('second_class', {}).get('name')}",
                    "suggestions": _suggest_gap_activities(gap_minutes, filters_dict)
                })
        
        # Step 6: Enhancement suggestions for web search
        enhancement_suggestions = []
        if len(filtered_pairs) < 3:
            enhancement_suggestions.append({
                "type": "web_search_needed",
                "reason": "Limited options in database",
                "suggested_queries": [f"{query} {filters_dict.get('location', '')}", f"kids activities {filters_dict.get('location', '')}"],
                "mix_strategy": "db_plus_web"
            })
        
        if not any(pair.get("gap_type") == "meal_break" for pair in filtered_pairs):
            enhancement_suggestions.append({
                "type": "meal_planning",
                "reason": "No meal breaks identified",
                "suggestion": "Consider adding restaurant/cafe search between activities",
                "mix_strategy": "db_plus_local_search"
            })
        
        return {
            "status": "success",
            "schedule_type": "full_day_planning",
            "total_db_activities": len(db_activities),
            "viable_pairs": len(filtered_pairs),
            "schedule_analysis": schedule_analysis,
            "time_gaps": time_gaps,
            "enhancement_suggestions": enhancement_suggestions,
            "recommended_pairs": filtered_pairs[:5],  # Top 5 recommendations
            "mixing_strategy": {
                "primary_source": "database",
                "enhancement_needed": len(enhancement_suggestions) > 0,
                "next_steps": enhancement_suggestions
            }
        }
        
    except Exception as e:
        logger.error(f"Error in plan_full_day_schedule: {e}", exc_info=True)
        return {"status": "error", "message": "An error occurred planning the day schedule"}

def _suggest_gap_activities(gap_minutes: int, filters: Dict[str, Any]) -> List[str]:
    """
    Suggest activities that could fill time gaps based on duration.
    """
    age = filters.get("age", 5)
    location = filters.get("location", "")
    
    if gap_minutes <= 30:
        return ["playground visit", "snack break", "quick walk"]
    elif gap_minutes <= 60:
        return ["lunch break", "park visit", "library story time"]
    elif gap_minutes <= 90:
        return ["museum visit", "shopping mall play area", "ice cream outing"]
    else:
        return ["movie screening", "arcade visit", "extended park time", "family restaurant visit"]
