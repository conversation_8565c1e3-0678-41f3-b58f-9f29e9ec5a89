"""
Intelligent Activity Tools - Token-Efficient Architecture
Uses smart tools to offload heavy processing from LLM to save tokens and costs.
"""

import logging
import time
import hashlib
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from google.adk.tools import ToolContext

logger = logging.getLogger(__name__)

# Import existing search infrastructure
from .activity_search_tools import _perform_advanced_search, _simple_search_fallback, _create_qdrant_filter, ActivityFilters

# Simple response cache for API optimization (10 RPM limit)
_response_cache = {}
_cache_ttl = 1800  # 30 minutes

def _get_cache_key(query: str, filters: Dict[str, Any], extra: str = "") -> str:
    """Generate cache key for responses."""
    cache_input = f"{query.lower().strip()}|{str(sorted(filters.items()))}|{extra}"
    return hashlib.md5(cache_input.encode()).hexdigest()

def _get_cached_response(cache_key: str) -> Optional[Any]:
    """Get cached response if available and not expired."""
    if cache_key in _response_cache:
        response, timestamp = _response_cache[cache_key]
        if time.time() - timestamp < _cache_ttl:
            logger.info(f"🎯 Cache HIT for key: {cache_key[:8]}...")
            return response
        else:
            del _response_cache[cache_key]
    return None

def _cache_response(cache_key: str, response: Any) -> None:
    """Cache a response."""
    _response_cache[cache_key] = (response, time.time())
    logger.info(f"💾 Cached response for key: {cache_key[:8]}...")

class ActivityAnalyzer:
    """Smart analyzer that processes activity data without LLM involvement."""
    
    @staticmethod
    def find_patterns(activities: List[Dict], pattern_type: str) -> Dict[str, Any]:
        """
        Analyzes activities for specific patterns without sending data to LLM.
        Returns structured insights that LLM can easily interpret.
        """
        if pattern_type == "back_to_back":
            return ActivityAnalyzer._analyze_back_to_back(activities)
        elif pattern_type == "schedule_conflicts":
            return ActivityAnalyzer._analyze_conflicts(activities)
        elif pattern_type == "progression_paths":
            return ActivityAnalyzer._analyze_progressions(activities)
        elif pattern_type == "cost_optimization":
            return ActivityAnalyzer._analyze_costs(activities)
        else:
            return {"error": f"Unknown pattern type: {pattern_type}"}
    
    @staticmethod
    def _analyze_back_to_back(activities: List[Dict]) -> Dict[str, Any]:
        """Find day planning opportunities with flexible gaps for better user experience."""
        # Import the enhanced day planning function
        from .activity_search_tools import _find_day_planning_activities
        
        # Use the enhanced day planning logic
        day_planning_pairs = _find_day_planning_activities(activities)
        
        # Transform to analyzer format
        opportunities = []
        for pair in day_planning_pairs:
            first_class = pair.get("first_class", {})
            second_class = pair.get("second_class", {})
            
            opportunities.append({
                "facilities": pair.get("facilities"),  # Changed from "facility" to "facilities"
                "cross_facility": pair.get("cross_facility", False),
                "date": pair.get("date"),
                "gap_minutes": pair.get("time_gap_minutes"),
                "gap_type": pair.get("gap_type"),
                "gap_description": pair.get("gap_description"),
                "first_class": {
                    "name": first_class.get("name"),
                    "facility": first_class.get("facility"),
                    "time": f"{first_class.get('start_time_iso')} - {first_class.get('end_time_iso')}",
                    "price": first_class.get("price_numeric", 0),
                    "registration_url": first_class.get("activity_url", "")
                },
                "second_class": {
                    "name": second_class.get("name"),
                    "facility": second_class.get("facility"),
                    "time": f"{second_class.get('start_time_iso')} - {second_class.get('end_time_iso')}",
                    "price": second_class.get("price_numeric", 0),
                    "registration_url": second_class.get("activity_url", "")
                },
                "total_price": (first_class.get("price_numeric", 0) + second_class.get("price_numeric", 0)),
                "total_duration": pair.get("total_duration"),
                "compatibility_valid": pair.get("compatibility_valid", True)
            })
        
        return {
            "pattern_type": "day_planning",
            "total_opportunities": len(opportunities),
            "opportunities": opportunities[:8],  # Limit for token efficiency
            "summary": f"Found {len(opportunities)} day planning opportunities with flexible gaps (5min-3hrs) for better scheduling"
        }
    
    @staticmethod
    def _analyze_conflicts(activities: List[Dict]) -> Dict[str, Any]:
        """Analyze schedule conflicts."""
        conflicts = []
        # Implementation for conflict detection
        return {
            "pattern_type": "schedule_conflicts",
            "conflicts": conflicts,
            "summary": f"Found {len(conflicts)} potential schedule conflicts"
        }
    
    @staticmethod
    def _analyze_progressions(activities: List[Dict]) -> Dict[str, Any]:
        """Analyze skill progression paths."""
        progressions = {}
        # Group by activity type and analyze levels
        for activity in activities:
            name = activity.get("name", "")
            if "level" in name.lower():
                # Extract progression info
                pass
        
        return {
            "pattern_type": "progression_paths",
            "progressions": progressions,
            "summary": "Skill progression analysis"
        }
    
    @staticmethod
    def _analyze_costs(activities: List[Dict]) -> Dict[str, Any]:
        """Analyze cost optimization opportunities."""
        cost_analysis = {
            "cheapest_options": sorted(activities, key=lambda x: x.get("price_numeric", float('inf')))[:5],
            "price_ranges": {},
            "bulk_discounts": []
        }
        
        return {
            "pattern_type": "cost_optimization",
            "analysis": cost_analysis,
            "summary": "Cost optimization analysis"
        }

# Smart Tools that use ActivityAnalyzer

async def discover_activity_patterns(
    query: str,
    filters: Dict[str, Any],
    pattern_types: List[str],
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Intelligent pattern discovery tool that processes data efficiently.
    
    Args:
        query: Search query for activities
        filters: Activity filters (age, location, etc.)
        pattern_types: List of patterns to discover (back_to_back, conflicts, progressions, costs)
    
    Returns:
        Structured insights without heavy token usage
    """
    try:
        # Validate filters
        validated_filters = ActivityFilters.model_validate(filters or {})
        filters_dict = validated_filters.model_dump(exclude_none=True)
        
        logger.info(f"🔍 Discovering patterns: {pattern_types} for query: '{query}'")
        
        # Get activities using Qdrant's server-side embeddings
        qdrant_filter = _create_qdrant_filter(filters_dict)
        activities = await _perform_advanced_search(query, qdrant_filter, limit=50)
        
        if not activities:
            return {
                "status": "success",
                "message": "No activities found matching your criteria",
                "patterns": {}
            }
        
        # Analyze patterns efficiently (no LLM involved)
        patterns = {}
        for pattern_type in pattern_types:
            patterns[pattern_type] = ActivityAnalyzer.find_patterns(activities, pattern_type)
        
        # Return concise, structured results
        return {
            "status": "success",
            "total_activities_analyzed": len(activities),
            "patterns": patterns,
            "query_info": {
                "search_query": query,
                "filters_applied": filters_dict,
                "patterns_requested": pattern_types
            }
        }
        
    except Exception as e:
        logger.error(f"Error in discover_activity_patterns: {e}", exc_info=True)
        return {"status": "error", "message": "An error occurred during pattern discovery"}

async def get_activity_summary(
    query: str,
    filters: Dict[str, Any],
    summary_type: str,
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Get concise activity summary without sending full data to LLM.
    
    Args:
        query: Search query
        filters: Activity filters
        summary_type: Type of summary (quick, detailed, comparison)
    
    Returns:
        Structured summary ready for LLM interpretation
    """
    try:
        validated_filters = ActivityFilters.model_validate(filters or {})
        filters_dict = validated_filters.model_dump(exclude_none=True)
        
        logger.info(f"📊 Generating {summary_type} summary for: '{query}'")
        
        # Get activities using Qdrant's server-side embeddings
        qdrant_filter = _create_qdrant_filter(filters_dict)
        limit = 20 if summary_type == "quick" else 40
        activities = await _perform_advanced_search(query, qdrant_filter, limit=limit)
        
        if not activities:
            return {
                "status": "success",
                "summary_type": summary_type,
                "message": "No activities found",
                "stats": {"total_found": 0}
            }
        
        # Generate summary statistics (no LLM needed)
        facilities = set(a.get("facility") for a in activities if a.get("facility"))
        categories = set(a.get("category") for a in activities if a.get("category"))
        price_range = {
            "min": min((a.get("price_numeric", 0) for a in activities if a.get("price_numeric")), default=0),
            "max": max((a.get("price_numeric", 0) for a in activities if a.get("price_numeric")), default=0),
            "average": sum(a.get("price_numeric", 0) for a in activities) / len(activities) if activities else 0
        }
        
        # Sample activities for LLM (much smaller payload)
        sample_activities = activities[:5] if summary_type == "quick" else activities[:10]
        
        return {
            "status": "success",
            "summary_type": summary_type,
            "stats": {
                "total_found": len(activities),
                "facilities_count": len(facilities),
                "categories_count": len(categories),
                "price_range": price_range
            },
            "facilities": list(facilities),
            "categories": list(categories),
            "sample_activities": [
                {
                    "name": a.get("name"),
                    "facility": a.get("facility"),
                    "time": f"{a.get('start_time_iso')} - {a.get('end_time_iso')}",
                    "date": a.get("start_date"),
                    "price": a.get("price_numeric"),
                    "age_range": a.get("display_age")
                }
                for a in sample_activities
            ]
        }
        
    except Exception as e:
        logger.error(f"Error in get_activity_summary: {e}", exc_info=True)
        return {"status": "error", "message": "An error occurred generating summary"}

async def check_activity_availability(
    activity_names: List[str],
    preferred_times: List[str],
    filters: Dict[str, Any],
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Check availability for specific activities at preferred times.
    Efficient tool that returns yes/no answers without heavy data.
    """
    try:
        validated_filters = ActivityFilters.model_validate(filters or {})
        filters_dict = validated_filters.model_dump(exclude_none=True)
        
        availability_results = {}
        
        for activity_name in activity_names:
            # Search for specific activity using Qdrant's server-side embeddings
            qdrant_filter = _create_qdrant_filter(filters_dict)
            activities = await _perform_advanced_search(activity_name, qdrant_filter, limit=20)
            
            # Check time preferences
            available_times = []
            for activity in activities:
                start_time = activity.get("start_time_iso")
                if start_time:
                    available_times.append({
                        "time": start_time,
                        "date": activity.get("start_date"),
                        "facility": activity.get("facility"),
                        "is_open": activity.get("is_open", False)
                    })
            
            # Match with preferred times
            matches = []
            for pref_time in preferred_times:
                for avail in available_times:
                    if pref_time in avail["time"] and avail["is_open"]:
                        matches.append(avail)
            
            availability_results[activity_name] = {
                "total_found": len(activities),
                "available_at_preferred_times": len(matches),
                "matches": matches[:3],  # Limit results
                "has_availability": len(matches) > 0
            }
        
        return {
            "status": "success",
            "availability": availability_results,
            "summary": f"Checked availability for {len(activity_names)} activities"
        }
        
    except Exception as e:
        logger.error(f"Error in check_activity_availability: {e}", exc_info=True)
        return {"status": "error", "message": "An error occurred checking availability"}

async def plan_full_day_schedule(
    query: str,
    filters: Dict[str, Any],
    preferred_gaps: List[str],  # ["quick", "meal", "extended"]
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Intelligent day planning tool that can mix and match activities from different sources.
    Uses agent's decision-making power to create optimal schedules.
    
    Args:
        query: What kind of activities to plan for
        filters: Activity filters (age, location, etc.)
        preferred_gaps: Preferred gap types between activities
    
    Returns:
        Comprehensive day schedule with mixed sources (DB + web search if needed)
    """
    try:
        validated_filters = ActivityFilters.model_validate(filters or {})
        filters_dict = validated_filters.model_dump(exclude_none=True)
        
        logger.info(f"🎯 Planning full day schedule for: '{query}' with gaps: {preferred_gaps}")
        
        # Step 1: Get base activities from database
        qdrant_filter = _create_qdrant_filter(filters_dict)
        db_activities = await _perform_advanced_search(query, qdrant_filter, limit=50)
        
        # Step 2: Use enhanced day planning
        from .activity_search_tools import _find_day_planning_activities
        day_planning_pairs = _find_day_planning_activities(db_activities)
        
        # Step 3: Filter by preferred gaps
        filtered_pairs = []
        for pair in day_planning_pairs:
            gap_type = pair.get("gap_type", "")
            if not preferred_gaps or gap_type in preferred_gaps:
                filtered_pairs.append(pair)
        
        # Step 4: Intelligent gap analysis
        schedule_analysis = {
            "morning_slots": [],
            "afternoon_slots": [],
            "evening_slots": [],
            "gaps_identified": [],
            "enhancement_suggestions": []
        }
        
        # Analyze time slots
        for pair in filtered_pairs:
            first_class = pair.get("first_class", {})
            start_time = first_class.get("start_time_iso", "")
            
            if start_time:
                hour = int(start_time.split(":")[0]) if ":" in start_time else 12
                if hour < 12:
                    schedule_analysis["morning_slots"].append(pair)
                elif hour < 17:
                    schedule_analysis["afternoon_slots"].append(pair)
                else:
                    schedule_analysis["evening_slots"].append(pair)
        
        # Step 5: Identify gaps that could be filled
        all_activities = db_activities[:20]  # Sample for gap analysis
        time_gaps = []
        
        for pair in filtered_pairs:
            gap_minutes = pair.get("time_gap_minutes", 0)
            if gap_minutes > 60:  # Long gaps that could be filled
                time_gaps.append({
                    "gap_duration": gap_minutes,
                    "gap_type": pair.get("gap_type"),
                    "between_activities": f"{pair.get('first_class', {}).get('name')} and {pair.get('second_class', {}).get('name')}",
                    "suggestions": _suggest_gap_activities(gap_minutes, filters_dict)
                })
        
        # Step 6: Enhancement suggestions for web search
        enhancement_suggestions = []
        if len(filtered_pairs) < 3:
            enhancement_suggestions.append({
                "type": "web_search_needed",
                "reason": "Limited options in database",
                "suggested_queries": [f"{query} {filters_dict.get('location', '')}", f"kids activities {filters_dict.get('location', '')}"],
                "mix_strategy": "db_plus_web"
            })
        
        if not any(pair.get("gap_type") == "meal_break" for pair in filtered_pairs):
            enhancement_suggestions.append({
                "type": "meal_planning",
                "reason": "No meal breaks identified",
                "suggestion": "Consider adding restaurant/cafe search between activities",
                "mix_strategy": "db_plus_local_search"
            })
        
        return {
            "status": "success",
            "schedule_type": "full_day_planning",
            "total_db_activities": len(db_activities),
            "viable_pairs": len(filtered_pairs),
            "schedule_analysis": schedule_analysis,
            "time_gaps": time_gaps,
            "enhancement_suggestions": enhancement_suggestions,
            "recommended_pairs": filtered_pairs[:5],  # Top 5 recommendations
            "mixing_strategy": {
                "primary_source": "database",
                "enhancement_needed": len(enhancement_suggestions) > 0,
                "next_steps": enhancement_suggestions
            }
        }
        
    except Exception as e:
        logger.error(f"Error in plan_full_day_schedule: {e}", exc_info=True)
        return {"status": "error", "message": "An error occurred planning the day schedule"}

def _suggest_gap_activities(gap_minutes: int, filters: Dict[str, Any]) -> List[str]:
    """
    Suggest activities that could fill time gaps based on duration.
    """
    age = filters.get("age", 5)
    location = filters.get("location", "")
    
    if gap_minutes <= 30:
        return ["playground visit", "snack break", "quick walk"]
    elif gap_minutes <= 60:
        return ["lunch break", "park visit", "library story time"]
    elif gap_minutes <= 90:
        return ["museum visit", "shopping mall play area", "ice cream outing"]
    else:
        return ["movie screening", "arcade visit", "extended park time", "family restaurant visit"]
