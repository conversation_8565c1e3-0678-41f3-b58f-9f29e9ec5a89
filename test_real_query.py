#!/usr/bin/env python3
"""
Real test of the multi-child query that was failing before.
Tests the exact query: "any activities that a 3 year old and a 6 year old can take together?"
"""

import asyncio
import json
import websockets
import time

async def test_real_user_query():
    """Test the actual user query through the WebSocket API."""

    # The exact query that was failing before
    user_query = "any activities that a 3 year old and a 6 year old can take together?"

    print(f"🧪 Testing real user query: '{user_query}'")
    print("=" * 80)

    # WebSocket URL
    ws_url = "ws://localhost:8080/ws"

    try:
        print("📡 Connecting to WebSocket...")

        async with websockets.connect(ws_url) as websocket:
            print("✅ WebSocket connected")

            # Send the user query
            message = {
                "type": "user_message",
                "content": user_query,
                "session_id": f"test_session_{int(time.time())}"
            }

            await websocket.send(json.dumps(message))
            print(f"📤 Sent query: {user_query}")

            # Collect the response
            agent_response = ""
            response_complete = False

            print("📥 Receiving response...")

            while not response_complete:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                    data = json.loads(response)

                    if data.get("type") == "text_chunk":
                        chunk = data.get("content", "")
                        agent_response += chunk
                        print(".", end="", flush=True)  # Progress indicator

                    elif data.get("type") == "turn_complete":
                        response_complete = True
                        print("\n✅ Response complete")

                except asyncio.TimeoutError:
                    print("\n⏰ Response timeout")
                    break
                except Exception as e:
                    print(f"\n❌ Error receiving response: {e}")
                    break

            if agent_response:
                print("✅ Agent Response:")
                print("-" * 60)
                print(agent_response)
                print("-" * 60)

                # Check for issues we were trying to fix
                issues_found = []

                # 1. Check for tool call artifacts
                if "<|tool_calls_section" in agent_response or "<|tool_calls_sectio" in agent_response:
                    issues_found.append("❌ Tool call artifacts still present")
                else:
                    print("✅ No tool call artifacts found")

                # 2. Check for registration links
                if "activity_url" in agent_response or "Registration:" in agent_response or "http" in agent_response:
                    print("✅ Registration links included")
                else:
                    issues_found.append("⚠️ No registration links found")

                # 3. Check for age information
                if "age" in agent_response.lower() and ("3" in agent_response or "6" in agent_response):
                    print("✅ Age information included")
                else:
                    issues_found.append("⚠️ Age information may be missing")

                # 4. Check for concurrent/multi-child thinking
                concurrent_keywords = ["together", "both", "concurrent", "same time", "different classes"]
                if any(keyword in agent_response.lower() for keyword in concurrent_keywords):
                    print("✅ Multi-child scheduling logic detected")
                else:
                    issues_found.append("⚠️ Limited multi-child scheduling thinking")

                # 5. Check for specific activity details
                if "location:" in agent_response.lower() or "schedule:" in agent_response.lower():
                    print("✅ Detailed activity information provided")
                else:
                    issues_found.append("⚠️ Limited activity details")

                # Summary
                print("\n" + "=" * 60)
                if issues_found:
                    print("⚠️ ISSUES FOUND:")
                    for issue in issues_found:
                        print(f"   {issue}")
                else:
                    print("🎉 ALL CHECKS PASSED!")

                print(f"\n📊 Response length: {len(agent_response)} characters")
                print(f"📊 Response quality: {'HIGH' if len(issues_found) <= 1 else 'MEDIUM' if len(issues_found) <= 3 else 'LOW'}")

                return len(issues_found) == 0
            else:
                print("❌ No response received from agent")
                return False
                    
    except Exception as e:
        print(f"❌ Request failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the real user query test."""
    print("🚀 Starting real user query test...")
    print("Testing the exact query that was failing before:")
    print("'any activities that a 3 year old and a 6 year old can take together?'")
    print()
    
    # Test the query
    success = await test_real_user_query()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 REAL TEST PASSED!")
        print("✅ The multi-child query is now working correctly")
        print("✅ All improvements have been successfully implemented")
    else:
        print("❌ REAL TEST FAILED!")
        print("⚠️ Some issues still need to be addressed")
    
    return success

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
