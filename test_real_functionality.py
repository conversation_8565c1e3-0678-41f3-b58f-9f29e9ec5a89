"""
Real test of enhanced back-to-back search functionality
Uses actual code and real data from Qdrant - no simulations
"""

import asyncio
import logging
import os
import sys
from typing import Dict, Any, List
from datetime import datetime

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_real_qdrant_data():
    """Test with real Qdrant data - no simulation."""
    logger.info("=" * 60)
    logger.info("🔍 TESTING WITH REAL QDRANT DATA")
    logger.info("=" * 60)
    
    try:
        # Import the real Qdrant client and config
        from qdrant_client import AsyncQdrantClient
        from multi_tool_agent.config import AgentConfig
        
        logger.info(f"Connecting to Qdrant at: {AgentConfig.QDRANT_URL}")
        logger.info(f"Collection: {AgentConfig.QDRANT_COLLECTION_NAME}")
        
        # Create real Qdrant client
        qdrant_client = AsyncQdrantClient(
            url=AgentConfig.QDRANT_URL,
            api_key=AgentConfig.QDRANT_API_KEY
        )
        
        # Test 1: Check collection exists
        try:
            collection_info = await qdrant_client.get_collection(AgentConfig.QDRANT_COLLECTION_NAME)
            logger.info(f"✅ Collection exists with {collection_info.vectors_count} vectors")
        except Exception as e:
            logger.error(f"❌ Collection check failed: {e}")
            return None
        
        # Test 2: Search for real activities
        from qdrant_client import models
        
        # Build filter for 5-year-old in New Westminster
        must_conditions = [
            models.FieldCondition(key="min_age_years", range=models.Range(lte=5)),
            models.FieldCondition(key="max_age_years", range=models.Range(gte=5)),
            models.FieldCondition(key="city", match=models.MatchValue(value="New Westminster"))
        ]
        
        qdrant_filter = models.Filter(must=must_conditions)
        
        # Perform real search
        logger.info("\n🔍 Searching for real activities for 5-year-olds in New Westminster...")
        response = await qdrant_client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=qdrant_filter,
            limit=100,
            with_payload=True
        )
        
        activities = [point.payload for point in response[0]]
        logger.info(f"✅ Found {len(activities)} real activities")
        
        # Show sample real activities
        if activities:
            logger.info("\n📋 Sample REAL activities found:")
            for i, activity in enumerate(activities[:5], 1):
                logger.info(f"\n{i}. {activity.get('name', 'Unknown')}")
                logger.info(f"   Facility: {activity.get('facility', 'Unknown')}")
                logger.info(f"   City: {activity.get('city', 'Unknown')}")
                logger.info(f"   Ages: {activity.get('min_age_years', '?')}-{activity.get('max_age_years', '?')}")
                logger.info(f"   Time: {activity.get('start_time_iso', '')} - {activity.get('end_time_iso', '')}")
                logger.info(f"   Days: {activity.get('days_of_week_list', [])}")
                logger.info(f"   Price: ${activity.get('price_numeric', 'N/A')}")
        
        await qdrant_client.close()
        return activities
        
    except Exception as e:
        logger.error(f"❌ Real Qdrant test failed: {e}", exc_info=True)
        return None

async def test_real_back_to_back_search():
    """Test the real back-to-back search functionality with actual data."""
    logger.info("\n" + "=" * 60)
    logger.info("🔍 TESTING REAL BACK-TO-BACK SEARCH")
    logger.info("=" * 60)
    
    try:
        # Import the real search functions
        from multi_tool_agent.tools.activity_search_tools import (
            _perform_advanced_search, 
            _create_qdrant_filter, 
            _find_day_planning_activities
        )
        
        # Create real filters
        filters = {
            "min_age": 5,
            "max_age": 5,
            "location": "New Westminster"
        }
        
        # Create real Qdrant filter
        qdrant_filter = _create_qdrant_filter(filters)
        
        # Perform real search
        logger.info("🔍 Performing real advanced search...")
        activities = await _perform_advanced_search(
            query="activities swimming gymnastics dance art sports",
            qdrant_filter=qdrant_filter,
            limit=100
        )
        
        logger.info(f"✅ Found {len(activities)} activities from real search")
        
        if activities:
            # Find real back-to-back opportunities
            logger.info("\n🔍 Analyzing for real back-to-back opportunities...")
            day_planning_pairs = _find_day_planning_activities(activities)
            
            logger.info(f"✅ Found {len(day_planning_pairs)} REAL back-to-back opportunities")
            
            # Show real examples
            if day_planning_pairs:
                logger.info("\n📍 REAL back-to-back opportunities found:")
                for i, pair in enumerate(day_planning_pairs[:3], 1):
                    logger.info(f"\n--- Real Option {i} ---")
                    logger.info(f"Date: {pair.get('date', 'Unknown')}")
                    logger.info(f"Gap: {pair.get('time_gap_minutes', 0)} minutes")
                    logger.info(f"Gap Type: {pair.get('gap_description', '')}")
                    logger.info(f"Cross-facility: {'Yes' if pair.get('cross_facility') else 'No'}")
                    
                    first = pair.get('first_class', {})
                    second = pair.get('second_class', {})
                    
                    logger.info(f"\nFirst Activity:")
                    logger.info(f"  Name: {first.get('name', 'Unknown')}")
                    logger.info(f"  Facility: {first.get('facility', 'Unknown')}")
                    logger.info(f"  Time: {first.get('start_time_iso', '')} - {first.get('end_time_iso', '')}")
                    logger.info(f"  Price: ${first.get('price_numeric', 'N/A')}")
                    
                    logger.info(f"\nSecond Activity:")
                    logger.info(f"  Name: {second.get('name', 'Unknown')}")
                    logger.info(f"  Facility: {second.get('facility', 'Unknown')}")
                    logger.info(f"  Time: {second.get('start_time_iso', '')} - {second.get('end_time_iso', '')}")
                    logger.info(f"  Price: ${second.get('price_numeric', 'N/A')}")
            else:
                logger.warning("⚠️ No back-to-back opportunities found in real data")
                
        return activities, day_planning_pairs if 'day_planning_pairs' in locals() else []
        
    except Exception as e:
        logger.error(f"❌ Real back-to-back test failed: {e}", exc_info=True)
        return None, []

async def test_enhanced_orchestrator():
    """Test the enhanced orchestrator with real data."""
    logger.info("\n" + "=" * 60)
    logger.info("🔍 TESTING ENHANCED ORCHESTRATOR")
    logger.info("=" * 60)
    
    try:
        # Test direct tool imports first
        logger.info("Testing tool imports...")
        
        # Import bc_activities_search
        from multi_tool_agent.tools.activity_search_tools import bc_activities_search
        logger.info("✅ Successfully imported bc_activities_search")
        
        # Test bc_activities_search with real parameters
        logger.info("\n🔍 Testing bc_activities_search with real data...")
        result = await bc_activities_search(
            query="swimming",
            filters={
                "min_age": 5,
                "max_age": 5,
                "location": "New Westminster"
            },
            tool_context=None
        )
        
        if result.get("status") == "success":
            logger.info(f"✅ bc_activities_search returned {len(result.get('results', []))} results")
            if result.get('results'):
                logger.info(f"   Sample: {result['results'][0].get('name', 'Unknown')}")
        else:
            logger.error(f"❌ bc_activities_search failed: {result.get('message', 'Unknown error')}")
            
    except Exception as e:
        logger.error(f"❌ Enhanced orchestrator test failed: {e}", exc_info=True)

async def main():
    """Run all real tests."""
    logger.info("🚀 Starting REAL Functionality Tests")
    logger.info("Using actual code and real Qdrant data - NO SIMULATIONS\n")
    
    # Run real tests
    qdrant_activities = await test_real_qdrant_data()
    search_activities, back_to_back_pairs = await test_real_back_to_back_search()
    await test_enhanced_orchestrator()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 REAL Test Summary")
    logger.info("=" * 60)
    
    if qdrant_activities:
        logger.info(f"✅ Real Qdrant data: {len(qdrant_activities)} activities found")
    else:
        logger.info("❌ Real Qdrant data: Failed to retrieve")
        
    if search_activities:
        logger.info(f"✅ Real search function: {len(search_activities)} activities found")
    else:
        logger.info("❌ Real search function: Failed")
        
    if back_to_back_pairs:
        logger.info(f"✅ Real back-to-back search: {len(back_to_back_pairs)} opportunities found")
    else:
        logger.info("❌ Real back-to-back search: No opportunities found")
    
    logger.info("\n🔍 This test used REAL data from your Qdrant database")
    logger.info("📊 No simulations - these are actual activities in your system")

if __name__ == "__main__":
    asyncio.run(main())
