#!/usr/bin/env python3
"""
Test ADK's Lite<PERSON><PERSON> wrapper with various model configurations
"""

import os
import asyncio
import logging
from google.adk.models.lite_llm import LiteLlm
from google.genai.types import Content, Part

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Mock context class
class MockContext:
    def __init__(self):
        self.session_id = "test_session"
        self.user_id = "test_user"

async def test_model_config(model_name: str, env_setup: dict = None):
    """Test a specific model configuration"""
    logger.info(f"\n🧪 Testing model: {model_name}")
    
    # Set up environment if provided
    if env_setup:
        for key, value in env_setup.items():
            os.environ[key] = value
            logger.info(f"   Set {key}={value}")
    
    try:
        # Create the model
        model = LiteLlm(model=model_name)
        logger.info(f"✅ Model created successfully")
        
        # Create test content
        content = Content(
            role="user",
            parts=[Part(text="Say 'Hello' and nothing else.")]
        )
        
        # Create mock context
        ctx = MockContext()
        
        # Try to generate content
        response_count = 0
        async for response in model.generate_content_async(content, ctx, stream=True):
            response_count += 1
            if response_count == 1:
                logger.info(f"✅ Received first response chunk")
            if response_count >= 3:
                break
                
        logger.info(f"✅ Successfully received {response_count} response chunks")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed: {type(e).__name__}: {str(e)}")
        return False

async def main():
    """Test various model configurations"""
    logger.info("🚀 Testing ADK LiteLlm with different model configurations...")
    
    # Check if API keys are set
    novita_key = os.getenv("NOVITA_API_KEY")
    if not novita_key:
        logger.warning("⚠️  NOVITA_API_KEY not set - tests may fail")
    
    # Test 1: Novita with provider prefix
    logger.info("\n=== Test 1: Novita with provider prefix ===")
    os.environ["NOVITA_API_KEY"] = novita_key or "test-key"
    await test_model_config("novita/moonshotai/kimi-k2-instruct")
    
    # Test 2: OpenAI format with Novita endpoint
    logger.info("\n=== Test 2: OpenAI format with Novita endpoint ===")
    await test_model_config(
        "openai/moonshotai/kimi-k2-instruct",
        env_setup={
            "OPENAI_API_KEY": novita_key or "test-key",
            "OPENAI_API_BASE": "https://api.novita.ai/v3/openai"
        }
    )
    
    # Test 3: Direct model name (no provider prefix)
    logger.info("\n=== Test 3: Direct model name ===")
    await test_model_config(
        "moonshotai/kimi-k2-instruct",
        env_setup={
            "OPENAI_API_KEY": novita_key or "test-key",
            "OPENAI_API_BASE": "https://api.novita.ai/v3/openai"
        }
    )
    
    # Test 4: Custom provider approach
    logger.info("\n=== Test 4: Custom provider registration ===")
    try:
        import litellm
        
        # Register Novita as a custom provider
        litellm.register_model({
            "novita/kimi-k2": {
                "max_tokens": 131072,
                "max_input_tokens": 131072,
                "max_output_tokens": 131072,
                "input_cost_per_token": 0.00057,
                "output_cost_per_token": 0.0023,
                "litellm_provider": "openai",
                "base_url": "https://api.novita.ai/v3/openai",
                "api_key": novita_key or "test-key",
                "model": "moonshotai/kimi-k2-instruct"
            }
        })
        
        await test_model_config("novita/kimi-k2")
    except Exception as e:
        logger.error(f"❌ Custom provider registration failed: {e}")
    
    # Test 5: Check if we need to use a different approach
    logger.info("\n=== Test 5: Alternative approach ===")
    # Since Novita uses OpenAI-compatible endpoint, we might need to use openai provider
    await test_model_config(
        "openai/moonshotai/kimi-k2-instruct",
        env_setup={
            "OPENAI_API_KEY": novita_key or "test-key",
            "OPENAI_API_BASE": "https://api.novita.ai/v3/openai"
        }
    )
    
    logger.info("\n✅ All tests completed!")
    
    # Show which approach worked
    logger.info("\n📝 Summary:")
    logger.info("Based on the ADK documentation, you should:")
    logger.info("1. Use the 'openai/' prefix for OpenAI-compatible endpoints")
    logger.info("2. Set OPENAI_API_KEY and OPENAI_API_BASE environment variables")
    logger.info("3. Or register custom providers with litellm.register_model()")

if __name__ == "__main__":
    # Set UTF-8 for Windows
    os.environ["PYTHONUTF8"] = "1"
    
    asyncio.run(main())
