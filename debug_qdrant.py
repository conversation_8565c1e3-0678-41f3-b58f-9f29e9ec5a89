#!/usr/bin/env python3
"""
Debug script to understand the correct Qdrant API for semantic search.
"""

import asyncio
import logging
from typing import List, Dict, Optional, Any
from qdrant_client import AsyncQdrantClient, models
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_qdrant_api():
    """Debug the correct Qdrant API usage."""
    
    # Get configuration
    QDRANT_URL = os.environ.get("QDRANT_URL", "https://your-qdrant-url")
    QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")
    QDRANT_COLLECTION_NAME = "community_activities_v2"
    
    if not QDRANT_API_KEY:
        logger.error("QDRANT_API_KEY not found in environment")
        return
    
    qdrant_client = AsyncQdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY)
    
    try:
        # First, let's check the collection info
        logger.info("📋 Checking collection info...")
        collection_info = await qdrant_client.get_collection(QDRANT_COLLECTION_NAME)
        logger.info(f"Collection info: {collection_info}")
        
        # Check what vectors are available
        vectors_config = collection_info.config.vectors_config
        logger.info(f"Vectors config: {vectors_config}")
        
        # Try different methods for semantic search
        query = "swimming classes"
        
        # Method 1: Try search with explicit vector
        logger.info("🔍 Method 1: Using search() with vector...")
        try:
            # This would require us to generate the embedding ourselves
            # search_response = await qdrant_client.search(
            #     collection_name=QDRANT_COLLECTION_NAME,
            #     query_vector=("dense", [0.1] * 768),  # Dummy vector
            #     limit=5
            # )
            logger.info("Method 1 would require pre-computed embeddings")
        except Exception as e:
            logger.error(f"Method 1 failed: {e}")
        
        # Method 2: Try query_points with different parameters
        logger.info("🔍 Method 2: Using query_points...")
        try:
            # Check if there's a query_points method
            if hasattr(qdrant_client, 'query_points'):
                logger.info("query_points method exists")
                # Try to understand its signature
                import inspect
                sig = inspect.signature(qdrant_client.query_points)
                logger.info(f"query_points signature: {sig}")
            else:
                logger.info("query_points method does not exist")
        except Exception as e:
            logger.error(f"Method 2 inspection failed: {e}")
        
        # Method 3: Check available methods
        logger.info("🔍 Method 3: Checking available methods...")
        methods = [method for method in dir(qdrant_client) if not method.startswith('_')]
        search_methods = [method for method in methods if 'search' in method.lower() or 'query' in method.lower()]
        logger.info(f"Available search/query methods: {search_methods}")
        
        # Method 4: Try search with text directly (newer versions)
        logger.info("🔍 Method 4: Trying search with text...")
        try:
            # Some versions support text search directly
            search_response = await qdrant_client.search(
                collection_name=QDRANT_COLLECTION_NAME,
                query_text=query,
                limit=5,
                with_payload=True
            )
            logger.info(f"✅ Text search successful! Found {len(search_response)} results")
            return "text_search"
        except Exception as e:
            logger.error(f"Method 4 failed: {e}")
        
        # Method 5: Try using scroll as alternative
        logger.info("🔍 Method 5: Using scroll as fallback...")
        try:
            scroll_response = await qdrant_client.scroll(
                collection_name=QDRANT_COLLECTION_NAME,
                limit=5,
                with_payload=True
            )
            logger.info(f"✅ Scroll successful! Found {len(scroll_response[0])} results")
            return "scroll_search"
        except Exception as e:
            logger.error(f"Method 5 failed: {e}")
            
    except Exception as e:
        logger.error(f"❌ Error accessing Qdrant: {e}")
        return None
    finally:
        await qdrant_client.close()

if __name__ == "__main__":
    result = asyncio.run(debug_qdrant_api())
    print(f"Result: {result}")
