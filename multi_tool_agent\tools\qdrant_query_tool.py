from qdrant_client import QdrantClient, models
import json

class QdrantQueryTool:
    """
    Qdrant query tool for advanced vector search operations.
    This will handle special cases in querying and optimize search.
    """

    def __init__(self, host, api_key=None):
        self.client = QdrantClient(host=host, api_key=api_key)

    def search_collection(self, collection_name, query_vector=None, filter_conditions=None, limit=10):
        """
        Searches a collection with potential filter conditions and query vectors.
        """
        try:
            query = {
                "collection_name": collection_name,
                "with_payload": True,
                "limit": limit
            }
            
            if query_vector:
                query["query_vector"] = query_vector
            
            if filter_conditions:
                query["filter"] = filter_conditions

            response = self.client.search(**query)
            return response
        except Exception as e:
            raise Exception(f"Error during Qdrant search: {e}")

    def create_filter_conditions(self, **conditions):
        """
        Creates a filter condition object for query.
        """
        must_conditions = []

        for key, value in conditions.items():
            must_conditions.append(models.FieldCondition(key=key, match=models.MatchValue(value=value)))

        return models.Filter(must=must_conditions)
