# Targon API Integration for Kimi K2 Fallback

## Overview

This document explains how to integrate Targon as a secondary provider for Kimi K2 model, providing additional rate limit capacity when Novita AI is rate limited.

## Configuration

### 1. Add Targon API Key to `.env`

```env
# Existing Novita AI configuration
NOVITA_API_KEY=sk_wgmgzgPKAPLU4xGXeeykFwajSF0PypvlsPfQnZ5ouIU

# Add Targon configuration
TARGON_API_KEY=your_targon_api_key_here
```

### 2. How It Works

The system now has a three-tier fallback strategy:

1. **Primary**: Novita AI Kimi K2 (10 RPM limit)
2. **Secondary**: Targon Kimi K2 (also has rate limits, but separate pool)
3. **Tertiary**: Unlimited Gemini models (no rate limits)

### Fallback Logic

```
User Request
    ↓
Is it a strategic task? (reasoning, orchestration, final output)
    ↓ Yes
Try Novita Kimi K2
    ↓ Rate Limited?
Try Targon Kimi K2
    ↓ Rate Limited?
Use Gemini 2.5 Flash (unlimited)
```

## API Compatibility

Targon uses an OpenAI-compatible API endpoint, which works seamlessly with:

1. **LiteLLM**: Using the `openai/` prefix format
2. **ADK**: Through LiteLLM integration

### LiteLLM Configuration

```python
# Targon model configuration for LiteLLM
model = "openai/moonshotai/Kimi-K2-Instruct"
api_base = "https://api.targon.com/v1"
api_key = os.getenv("TARGON_API_KEY")
```

## Rate Limiting Strategy

### Current Limits
- **Novita AI**: 10 RPM for Kimi K2
- **Targon**: Also has rate limits (check your plan)
- **Combined**: Potentially 20 RPM if both are available
- **Gemini**: Unlimited requests

### Smart Usage Pattern

The system intelligently distributes requests:

1. **Strategic Tasks (30-50%)**: Use Kimi providers
   - Final output generation
   - Agent delegation and orchestration
   - Complex reasoning
   - Tool calling decisions

2. **Supporting Tasks (50-70%)**: Use unlimited Gemini
   - Simple tool execution
   - Data processing
   - Search operations
   - Intermediate tasks

## Monitoring

The API monitor tracks usage for both Kimi providers separately:

```json
{
  "current_novita_kimi_rpm": 8,
  "current_targon_kimi_rpm": 2,
  "total_kimi_rpm": 10,
  "calls_by_model": {
    "novita_kimi": 45,
    "targon_kimi": 12,
    "gemini-2.5-flash": 89
  }
}
```

Access monitoring at: `http://localhost:8080/api-stats`

## Testing Targon Integration

Run the test script to verify Targon is working:

```bash
python test_targon_litellm.py
```

Expected output:
```
🧪 Testing Targon with LiteLLM (OpenAI format)...
✅ Success! Response: Hello! How can I assist you today?

🧪 Testing Targon with streaming...
✅ Streaming response: 1, 2, 3, 4, 5.
```

## Troubleshooting

### Common Issues

1. **Authentication Error**
   - Verify `TARGON_API_KEY` is set in `.env`
   - Check API key is valid and has credits

2. **Rate Limit Error**
   - Targon also has rate limits based on your plan
   - System will automatically fall back to Gemini

3. **Connection Error**
   - Verify network connectivity to `api.targon.com`
   - Check if Targon API is operational

### Debug Logs

Enable debug logging to see which provider is being used:

```python
import logging
logging.basicConfig(level=logging.INFO)
```

Look for these log messages:
- `🧠 Using Novita Kimi K2 for reasoning`
- `🔄 Using Targon Kimi K2 as fallback for reasoning`
- `⚡ Using unlimited Gemini model for search`

## Benefits

1. **Increased Kimi Capacity**: Potentially double the Kimi requests (20 RPM combined)
2. **Better Reliability**: Redundancy if one provider has issues
3. **Cost Optimization**: Still uses unlimited Gemini for most tasks
4. **Seamless Integration**: No code changes needed in agents

## Future Improvements

1. **Dynamic Load Balancing**: Distribute requests between Novita and Targon based on current usage
2. **Provider Health Checks**: Automatically detect and skip unavailable providers
3. **Cost Tracking**: Monitor costs per provider
4. **Custom Rate Limits**: Configure different RPM limits per provider

## Summary

With Targon integration, you now have:
- **Primary Kimi provider**: Novita AI (10 RPM)
- **Secondary Kimi provider**: Targon (additional RPM based on plan)
- **Unlimited fallback**: Gemini models
- **Automatic failover**: Seamless switching between providers
- **Comprehensive monitoring**: Track usage across all providers

This provides a robust, scalable solution that maximizes Kimi K2 usage for strategic tasks while leveraging unlimited Gemini for supporting operations.
