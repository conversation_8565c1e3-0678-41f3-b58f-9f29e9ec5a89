"""
Test the activity compatibility rules for back-to-back classes
Verifies the restrictions are properly implemented
"""

import logging
from typing import Dict

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def _validate_activity_compatibility(first_activity: Dict, second_activity: Dict) -> bool:
    """
    Validates that activities can be taken together in a day.
    Rules based on proper level progression:
    1. A child can only take ONE level of a specific activity type (can't do Swimming Level 1 and Level 2)
    2. Different activity types are always compatible (Swimming Level 1 + Gymnastics Level 3 is OK)
    3. Non-leveled activities can be combined with anything
    4. Same exact class at same time is not allowed (duplicate booking)
    """
    def extract_activity_info(activity_name: str) -> tuple[str, int]:
        """Extract activity type and level from name like 'Swimming Level 02' -> ('Swimming', 2)"""
        import re
        # Look for 'Level XX' pattern
        level_match = re.search(r'Level\s+(\d+)', activity_name, re.IGNORECASE)
        level = int(level_match.group(1)) if level_match else 0
        
        # Extract activity type (everything before 'Level')
        if level > 0:
            activity_type = re.sub(r'\s+Level\s+\d+.*', '', activity_name, flags=re.IGNORECASE).strip()
        else:
            activity_type = activity_name.strip()
        
        return activity_type, level
    
    first_name = first_activity.get('name', '')
    second_name = second_activity.get('name', '')
    
    # Check if it's the exact same class at the exact same time (duplicate booking)
    if (first_name == second_name and 
        first_activity.get('start_time_iso') == second_activity.get('start_time_iso') and
        first_activity.get('start_date') == second_activity.get('start_date')):
        logger.info(f"❌ Duplicate booking: Same class at same time")
        return False
    
    first_type, first_level = extract_activity_info(first_name)
    second_type, second_level = extract_activity_info(second_name)
    
    # If both are leveled classes of the SAME activity type - NOT ALLOWED
    # A child can only take one level of a specific activity
    if (first_type.lower() == second_type.lower() and 
        first_level > 0 and second_level > 0):
        logger.info(f"❌ Can't take multiple levels of same activity: {first_name} and {second_name}")
        logger.info(f"   → A child must complete {first_type} Level {min(first_level, second_level)} before taking Level {max(first_level, second_level)}")
        return False
    
    # Different activity types are always compatible
    # e.g., Swimming Level 1 + Gymnastics Level 3 is perfectly fine
    if first_type.lower() != second_type.lower():
        if first_level > 0 and second_level > 0:
            logger.info(f"✅ Different activities at different levels: {first_name} + {second_name}")
        return True
    
    # Non-leveled activities are always compatible with everything
    return True

def test_compatibility_rules():
    """Test various activity combinations."""
    
    test_cases = [
        # Test 1: Same activity, different levels - NOT ALLOWED
        {
            "first": {"name": "Swimming Level 01 - Preschool", "start_time_iso": "10:00:00", "start_date": "2025-01-20"},
            "second": {"name": "Swimming Level 02 - Preschool", "start_time_iso": "11:00:00", "start_date": "2025-01-20"},
            "expected": False,
            "reason": "Can't take multiple swimming levels"
        },
        
        # Test 2: Different activities with levels - ALLOWED
        {
            "first": {"name": "Swimming Level 01 - Preschool", "start_time_iso": "10:00:00", "start_date": "2025-01-20"},
            "second": {"name": "Gymnastics Level 02", "start_time_iso": "11:00:00", "start_date": "2025-01-20"},
            "expected": True,
            "reason": "Different activities can have different levels"
        },
        
        # Test 3: Leveled + Non-leveled - ALLOWED
        {
            "first": {"name": "Swimming Level 03", "start_time_iso": "10:00:00", "start_date": "2025-01-20"},
            "second": {"name": "Creative Dance", "start_time_iso": "11:00:00", "start_date": "2025-01-20"},
            "expected": True,
            "reason": "Leveled activities can mix with non-leveled"
        },
        
        # Test 4: Same exact class at same time - NOT ALLOWED
        {
            "first": {"name": "Art Class", "start_time_iso": "14:00:00", "start_date": "2025-01-20"},
            "second": {"name": "Art Class", "start_time_iso": "14:00:00", "start_date": "2025-01-20"},
            "expected": False,
            "reason": "Duplicate booking not allowed"
        },
        
        # Test 5: Non-leveled activities - ALLOWED
        {
            "first": {"name": "Pottery Workshop", "start_time_iso": "10:00:00", "start_date": "2025-01-20"},
            "second": {"name": "Music Appreciation", "start_time_iso": "14:00:00", "start_date": "2025-01-20"},
            "expected": True,
            "reason": "Non-leveled activities are compatible"
        },
        
        # Test 6: Swimming Level 1 + Swimming Level 5 - NOT ALLOWED
        {
            "first": {"name": "Swimming Level 01", "start_time_iso": "09:00:00", "start_date": "2025-01-20"},
            "second": {"name": "Swimming Level 05", "start_time_iso": "10:00:00", "start_date": "2025-01-20"},
            "expected": False,
            "reason": "Can't skip swimming levels"
        },
        
        # Test 7: Gymnastics Level 1 + Dance Level 1 - ALLOWED
        {
            "first": {"name": "Gymnastics Level 1", "start_time_iso": "09:00:00", "start_date": "2025-01-20"},
            "second": {"name": "Dance Level 1", "start_time_iso": "10:30:00", "start_date": "2025-01-20"},
            "expected": True,
            "reason": "Different activities can both be Level 1"
        }
    ]
    
    logger.info("=" * 60)
    logger.info("🧪 TESTING ACTIVITY COMPATIBILITY RULES")
    logger.info("=" * 60)
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\nTest {i}: {test_case['reason']}")
        logger.info(f"  First: {test_case['first']['name']}")
        logger.info(f"  Second: {test_case['second']['name']}")
        
        result = _validate_activity_compatibility(test_case['first'], test_case['second'])
        expected = test_case['expected']
        
        if result == expected:
            logger.info(f"  ✅ PASSED - Result: {'Compatible' if result else 'Not Compatible'}")
            passed += 1
        else:
            logger.info(f"  ❌ FAILED - Expected: {'Compatible' if expected else 'Not Compatible'}, Got: {'Compatible' if result else 'Not Compatible'}")
            failed += 1
    
    logger.info("\n" + "=" * 60)
    logger.info(f"📊 TEST SUMMARY: {passed}/{len(test_cases)} passed")
    logger.info("=" * 60)
    
    if failed == 0:
        logger.info("🎉 All compatibility rules are working correctly!")
    else:
        logger.info(f"⚠️ {failed} tests failed - rules need adjustment")

if __name__ == "__main__":
    test_compatibility_rules()
