"""
Test real Qdrant data with proper compatibility rules
Shows only valid back-to-back opportunities following the restrictions
"""

import asyncio
import logging
from qdrant_client import AsyncQdrantClient, models
import os
from datetime import datetime
from typing import List, Dict, Any, Tuple
import re

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Get Qdrant credentials
QDRANT_URL = os.getenv("QDRANT_URL", "https://7193c1cf-a88e-4252-8b1b-ab480cdcd7d0.us-west-2-0.aws.cloud.qdrant.io:6333")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY", "")
QDRANT_COLLECTION_NAME = "community_activities_v2"

def extract_activity_info(activity_name: str) -> <PERSON>ple[str, int]:
    """Extract activity type and level from name."""
    level_match = re.search(r'Level\s+(\d+)', activity_name, re.IGNORECASE)
    level = int(level_match.group(1)) if level_match else 0
    
    if level > 0:
        activity_type = re.sub(r'\s+Level\s+\d+.*', '', activity_name, flags=re.IGNORECASE).strip()
    else:
        activity_type = activity_name.strip()
    
    return activity_type, level

def validate_compatibility(first: Dict, second: Dict) -> Tuple[bool, str]:
    """Check if two activities are compatible for back-to-back scheduling."""
    first_name = first.get('name', '')
    second_name = second.get('name', '')
    
    # Check duplicate booking
    if (first_name == second_name and 
        first.get('start_time_iso') == second.get('start_time_iso') and
        first.get('start_date') == second.get('start_date')):
        return False, "Duplicate booking"
    
    first_type, first_level = extract_activity_info(first_name)
    second_type, second_level = extract_activity_info(second_name)
    
    # Same activity type with levels - NOT ALLOWED
    if (first_type.lower() == second_type.lower() and 
        first_level > 0 and second_level > 0):
        return False, f"Can't take {first_type} Level {first_level} and Level {second_level} together"
    
    # Different activities or non-leveled - ALLOWED
    if first_type.lower() != second_type.lower():
        return True, "Different activities"
    
    return True, "Non-leveled or compatible"

async def get_real_activities():
    """Get real activities from Qdrant."""
    client = AsyncQdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY)
    
    try:
        # Search for 5-year-olds in New Westminster
        must_conditions = [
            models.FieldCondition(key="min_age_years", range=models.Range(lte=5)),
            models.FieldCondition(key="max_age_years", range=models.Range(gte=5)),
            models.FieldCondition(key="city", match=models.MatchValue(value="New Westminster"))
        ]
        
        qdrant_filter = models.Filter(must=must_conditions)
        
        response = await client.scroll(
            collection_name=QDRANT_COLLECTION_NAME,
            scroll_filter=qdrant_filter,
            limit=300,  # Get more to find diverse activities
            with_payload=True
        )
        
        activities = [point.payload for point in response[0]]
        await client.close()
        return activities
        
    except Exception as e:
        logger.error(f"Error: {e}")
        await client.close()
        return []

def find_compatible_back_to_back(activities: List[Dict]) -> List[Dict]:
    """Find back-to-back opportunities with proper compatibility checking."""
    opportunities = []
    
    # Group by date and facility
    by_date_facility = {}
    for activity in activities:
        if not all([
            activity.get('start_time_iso'),
            activity.get('end_time_iso'),
            activity.get('start_date'),
            activity.get('facility')
        ]):
            continue
            
        key = f"{activity['start_date']}|{activity['facility']}"
        if key not in by_date_facility:
            by_date_facility[key] = []
        by_date_facility[key].append(activity)
    
    # Find compatible pairs
    for key, acts in by_date_facility.items():
        if len(acts) < 2:
            continue
            
        # Sort by start time
        sorted_acts = sorted(acts, key=lambda x: x.get('start_time_iso', ''))
        
        for i in range(len(sorted_acts) - 1):
            act1 = sorted_acts[i]
            act2 = sorted_acts[i + 1]
            
            # Check compatibility
            compatible, reason = validate_compatibility(act1, act2)
            if not compatible:
                continue
            
            # Calculate gap
            try:
                end1 = datetime.strptime(act1['end_time_iso'], '%H:%M:%S')
                start2 = datetime.strptime(act2['start_time_iso'], '%H:%M:%S')
                gap_minutes = (start2 - end1).seconds / 60
                
                if 5 <= gap_minutes <= 180:  # 5 minutes to 3 hours
                    opportunities.append({
                        'date': act1['start_date'],
                        'facility': act1['facility'],
                        'gap_minutes': int(gap_minutes),
                        'first': act1,
                        'second': act2,
                        'compatibility': reason
                    })
            except:
                continue
    
    return opportunities

async def main():
    """Run the test with real data and proper compatibility rules."""
    logger.info("🚀 Testing Real Activities with Proper Compatibility Rules\n")
    
    # Get real activities
    activities = await get_real_activities()
    
    if not activities:
        logger.error("No activities found")
        return
    
    logger.info(f"✅ Found {len(activities)} activities for 5-year-olds in New Westminster")
    
    # Analyze activity types
    activity_types = {}
    leveled_activities = []
    non_leveled_activities = []
    
    for activity in activities:
        name = activity.get('name', '')
        activity_type, level = extract_activity_info(name)
        
        if level > 0:
            leveled_activities.append(activity)
            key = f"{activity_type} (Levels)"
        else:
            non_leveled_activities.append(activity)
            key = activity_type
        
        if key not in activity_types:
            activity_types[key] = 0
        activity_types[key] += 1
    
    logger.info(f"\n📊 Activity Breakdown:")
    logger.info(f"   - Leveled activities: {len(leveled_activities)}")
    logger.info(f"   - Non-leveled activities: {len(non_leveled_activities)}")
    
    logger.info(f"\n📋 Activity Types:")
    for activity_type, count in sorted(activity_types.items()):
        logger.info(f"   - {activity_type}: {count}")
    
    # Find compatible back-to-back opportunities
    logger.info("\n" + "=" * 60)
    logger.info("🔍 FINDING COMPATIBLE BACK-TO-BACK OPPORTUNITIES")
    logger.info("=" * 60)
    
    opportunities = find_compatible_back_to_back(activities)
    logger.info(f"\n✅ Found {len(opportunities)} COMPATIBLE back-to-back opportunities")
    
    # Categorize opportunities
    same_activity_pairs = []
    mixed_activity_pairs = []
    
    for opp in opportunities:
        first_type, first_level = extract_activity_info(opp['first']['name'])
        second_type, second_level = extract_activity_info(opp['second']['name'])
        
        if first_type.lower() == second_type.lower():
            same_activity_pairs.append(opp)
        else:
            mixed_activity_pairs.append(opp)
    
    logger.info(f"\n📊 Opportunity Types:")
    logger.info(f"   - Mixed activities (different types): {len(mixed_activity_pairs)}")
    logger.info(f"   - Same activity type (non-leveled or compatible): {len(same_activity_pairs)}")
    
    # Show examples of mixed activities
    if mixed_activity_pairs:
        logger.info("\n🌟 Best Back-to-Back Options (Mixed Activities):")
        for i, opp in enumerate(mixed_activity_pairs[:5], 1):
            logger.info(f"\n--- Option {i} ---")
            logger.info(f"Date: {opp['date']}")
            logger.info(f"Facility: {opp['facility']}")
            logger.info(f"Gap: {opp['gap_minutes']} minutes")
            
            first_type, first_level = extract_activity_info(opp['first']['name'])
            second_type, second_level = extract_activity_info(opp['second']['name'])
            
            logger.info(f"First: {opp['first']['name']} ({opp['first']['start_time_iso']} - {opp['first']['end_time_iso']})")
            logger.info(f"       Type: {first_type} {'Level ' + str(first_level) if first_level else '(non-leveled)'}")
            logger.info(f"Second: {opp['second']['name']} ({opp['second']['start_time_iso']} - {opp['second']['end_time_iso']})")
            logger.info(f"        Type: {second_type} {'Level ' + str(second_level) if second_level else '(non-leveled)'}")
    
    # Show non-swimming options if any
    non_swimming_pairs = [opp for opp in opportunities if 'swimming' not in opp['first']['name'].lower() and 'swimming' not in opp['second']['name'].lower()]
    
    if non_swimming_pairs:
        logger.info(f"\n🎨 Non-Swimming Back-to-Back Options: {len(non_swimming_pairs)}")
        for i, opp in enumerate(non_swimming_pairs[:3], 1):
            logger.info(f"\nOption {i}:")
            logger.info(f"  {opp['first']['name']} → {opp['second']['name']}")
            logger.info(f"  Gap: {opp['gap_minutes']} minutes")
    
    logger.info("\n✅ All shown options follow the compatibility rules:")
    logger.info("   • No multiple levels of the same activity")
    logger.info("   • Different activities can have different levels")
    logger.info("   • Non-leveled activities can mix with anything")

if __name__ == "__main__":
    asyncio.run(main())
