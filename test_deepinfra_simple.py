#!/usr/bin/env python3
"""
Simple test to verify DeepInfra fallback logic works correctly.
"""

import asyncio
import sys
import os
import logging

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from multi_tool_agent.config import AgentConfig
from multi_tool_agent.utils.rate_limiter import HybridModelManager, reset_hybrid_manager

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_configuration_migration():
    """Test that configuration was properly migrated."""
    logger.info("🔧 Testing DeepInfra configuration migration...")
    
    # Check config values
    print(f"DEEPINFRA_API_KEY: {AgentConfig.DEEPINFRA_API_KEY}")
    print(f"DEEPINFRA_API_BASE: {AgentConfig.DEEPINFRA_API_BASE}")
    print(f"DEEPINFRA_KIMI_MODEL: {AgentConfig.DEEPINFRA_KIMI_MODEL}")
    print(f"USE_DEEPINFRA_FALLBACK: {AgentConfig.USE_DEEPINFRA_FALLBACK}")
    print(f"DEEPINFRA_RATE_LIMIT_RPM: {AgentConfig.DEEPINFRA_RATE_LIMIT_RPM}")
    
    # Test manager initialization
    reset_hybrid_manager()
    manager = HybridModelManager()
    
    print(f"Manager DeepInfra config:")
    print(f"  deepinfra_api_base: {manager.deepinfra_api_base}")
    print(f"  deepinfra_kimi_model: {manager.deepinfra_kimi_model}")
    print(f"  use_deepinfra: {manager.use_deepinfra}")
    print(f"  deepinfra_limiter RPM: {manager.deepinfra_limiter.config.rpm}")
    print(f"  deepinfra_limiter provider: {manager.deepinfra_limiter.provider_name}")
    
    # Check that old Targon attributes are gone
    targon_attrs = ['targon_api_base', 'targon_kimi_model', 'use_targon', 'targon_limiter']
    for attr in targon_attrs:
        if hasattr(manager, attr):
            print(f"⚠️  Found old Targon attribute: {attr}")
        else:
            print(f"✅ Old Targon attribute {attr} is gone")
    
    logger.info("✅ Configuration migration test completed!")

async def test_rate_limiters():
    """Test that rate limiters are properly configured."""
    logger.info("🔧 Testing rate limiter configuration...")
    
    reset_hybrid_manager()
    manager = HybridModelManager()
    
    # Test Novita limiter
    novita_limiter = manager.kimi_limiter
    print(f"Novita limiter:")
    print(f"  Provider: {novita_limiter.provider_name}")
    print(f"  RPM: {novita_limiter.config.rpm}")
    print(f"  Min interval: {novita_limiter.config.min_interval}s")
    
    # Test DeepInfra limiter
    deepinfra_limiter = manager.deepinfra_limiter
    print(f"DeepInfra limiter:")
    print(f"  Provider: {deepinfra_limiter.provider_name}")
    print(f"  RPM: {deepinfra_limiter.config.rpm}")
    print(f"  Min interval: {deepinfra_limiter.config.min_interval}s")
    
    # Test that they're not rate limited initially
    novita_limited = await novita_limiter.is_rate_limited()
    deepinfra_limited = await deepinfra_limiter.is_rate_limited()
    
    print(f"Initial rate limit status:")
    print(f"  Novita: {'limited' if novita_limited else 'available'}")
    print(f"  DeepInfra: {'limited' if deepinfra_limited else 'available'}")
    
    logger.info("✅ Rate limiter configuration test completed!")

async def test_model_selection():
    """Test model selection logic."""
    logger.info("🔧 Testing model selection logic...")
    
    reset_hybrid_manager()
    manager = HybridModelManager()
    
    # Test different task types
    tasks = [
        ("reasoning", True),
        ("tool_execution", False),
        ("simple_response", False),
        ("orchestration", False),
        ("final_output", False),
    ]
    
    for task_type, is_critical in tasks:
        model = manager.get_model_for_task(task_type, is_critical)
        should_use = await manager.should_use_kimi(task_type, is_critical)
        print(f"Task '{task_type}' (critical={is_critical}): model={model}, use_kimi={should_use}")
    
    logger.info("✅ Model selection test completed!")

async def main():
    """Run all tests."""
    logger.info("🚀 Starting DeepInfra setup verification...")
    
    try:
        await test_configuration_migration()
        await test_rate_limiters()
        await test_model_selection()
        
        logger.info("🎉 All tests completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
