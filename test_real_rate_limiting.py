#!/usr/bin/env python3
"""
Real integration test to verify rate limiting is working correctly.
This test makes actual requests to the server to ensure rate limiting is enforced.
"""

import asyncio
import time
import logging
import aiohttp
import json
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Server configuration
SERVER_URL = "http://localhost:8080"
WS_URL = "ws://localhost:8080/ws"

async def test_single_request():
    """Test a single request to verify basic functionality."""
    logger.info("🧪 Testing single request...")
    
    session = aiohttp.ClientSession()
    try:
        # Connect to WebSocket
        async with session.ws_connect(WS_URL) as ws:
            # Send a simple query
            query = {
                "type": "chat_message",
                "content": "What swimming classes are available for a 5 year old?",
                "session_id": f"test_{int(time.time())}",
                "user_id": "test_user"
            }
            
            await ws.send_json(query)
            logger.info("📤 Sent query to server")
            
            # Read responses
            start_time = time.time()
            response_count = 0
            
            async for msg in ws:
                if msg.type == aiohttp.WSMsgType.TEXT:
                    data = json.loads(msg.data)
                    msg_type = data.get("type")
                    
                    if msg_type == "ack":
                        logger.info("✅ Received acknowledgment")
                    elif msg_type == "text_chunk":
                        response_count += 1
                        if response_count == 1:
                            logger.info("📝 Receiving response chunks...")
                    elif msg_type == "turn_complete":
                        elapsed = time.time() - start_time
                        logger.info(f"✅ Response complete in {elapsed:.2f}s")
                        break
                    elif msg_type == "error":
                        logger.error(f"❌ Error: {data.get('message')}")
                        break
                        
                elif msg.type == aiohttp.WSMsgType.ERROR:
                    logger.error(f"❌ WebSocket error: {ws.exception()}")
                    break
                    
    finally:
        await session.close()

async def test_rapid_requests(num_requests=15):
    """Test multiple rapid requests to verify rate limiting."""
    logger.info(f"🧪 Testing {num_requests} rapid requests...")
    
    session = aiohttp.ClientSession()
    results = []
    
    try:
        # Create multiple WebSocket connections
        tasks = []
        for i in range(num_requests):
            async def make_request(index):
                try:
                    async with session.ws_connect(WS_URL) as ws:
                        query = {
                            "type": "chat_message",
                            "content": f"Test query {index}: Find swimming classes",
                            "session_id": f"test_{index}_{int(time.time())}",
                            "user_id": f"test_user_{index}"
                        }
                        
                        start_time = time.time()
                        await ws.send_json(query)
                        
                        # Wait for response or error
                        async for msg in ws:
                            if msg.type == aiohttp.WSMsgType.TEXT:
                                data = json.loads(msg.data)
                                msg_type = data.get("type")
                                
                                if msg_type == "turn_complete":
                                    elapsed = time.time() - start_time
                                    results.append({
                                        "index": index,
                                        "success": True,
                                        "time": elapsed,
                                        "timestamp": time.time()
                                    })
                                    logger.info(f"✅ Request {index} completed in {elapsed:.2f}s")
                                    break
                                elif msg_type == "error":
                                    error_msg = data.get("message", "Unknown error")
                                    if "rate" in error_msg.lower() or "429" in error_msg:
                                        results.append({
                                            "index": index,
                                            "success": False,
                                            "error": "rate_limited",
                                            "timestamp": time.time()
                                        })
                                        logger.warning(f"🚫 Request {index} was rate limited")
                                    else:
                                        results.append({
                                            "index": index,
                                            "success": False,
                                            "error": error_msg,
                                            "timestamp": time.time()
                                        })
                                        logger.error(f"❌ Request {index} failed: {error_msg}")
                                    break
                                    
                except Exception as e:
                    results.append({
                        "index": index,
                        "success": False,
                        "error": str(e),
                        "timestamp": time.time()
                    })
                    logger.error(f"❌ Request {index} exception: {e}")
            
            # Small delay between starting connections
            await asyncio.sleep(0.1)
            task = asyncio.create_task(make_request(i))
            tasks.append(task)
        
        # Wait for all requests to complete
        await asyncio.gather(*tasks, return_exceptions=True)
        
    finally:
        await session.close()
    
    # Analyze results
    analyze_results(results)

def analyze_results(results):
    """Analyze test results to verify rate limiting."""
    logger.info("\n📊 ANALYSIS OF RATE LIMITING TEST")
    logger.info("=" * 50)
    
    # Sort by timestamp
    results.sort(key=lambda x: x['timestamp'])
    
    # Count successes and failures
    successful = [r for r in results if r.get('success', False)]
    rate_limited = [r for r in results if not r.get('success', False) and r.get('error') == 'rate_limited']
    other_errors = [r for r in results if not r.get('success', False) and r.get('error') != 'rate_limited']
    
    logger.info(f"Total requests: {len(results)}")
    logger.info(f"Successful: {len(successful)}")
    logger.info(f"Rate limited: {len(rate_limited)}")
    logger.info(f"Other errors: {len(other_errors)}")
    
    # Calculate RPM based on actual timestamps
    if len(results) >= 2:
        time_window = results[-1]['timestamp'] - results[0]['timestamp']
        if time_window > 0:
            actual_rpm = (len(successful) / time_window) * 60
            logger.info(f"Actual successful RPM: {actual_rpm:.1f}")
            
            if actual_rpm > 10:
                logger.warning("⚠️ RATE LIMITING NOT WORKING! Exceeded 10 RPM limit")
            else:
                logger.info("✅ Rate limiting is working correctly (under 10 RPM)")
    
    # Show timeline of requests
    logger.info("\nTimeline of requests:")
    start_time = results[0]['timestamp'] if results else 0
    for r in results:
        relative_time = r['timestamp'] - start_time
        status = "✅" if r.get('success') else "🚫" if r.get('error') == 'rate_limited' else "❌"
        logger.info(f"  {status} Request {r.get('index', '?')} at {relative_time:.2f}s")
    
    logger.info("=" * 50)

async def test_sustained_load(duration_seconds=70):
    """Test sustained load over more than 1 minute to verify RPM limit."""
    logger.info(f"🧪 Testing sustained load for {duration_seconds} seconds...")
    
    session = aiohttp.ClientSession()
    results = []
    start_time = time.time()
    request_count = 0
    
    try:
        while time.time() - start_time < duration_seconds:
            try:
                async with session.ws_connect(WS_URL) as ws:
                    query = {
                        "type": "chat_message",
                        "content": f"Query {request_count}: Find activities",
                        "session_id": f"sustained_{request_count}_{int(time.time())}",
                        "user_id": "sustained_test_user"
                    }
                    
                    req_start = time.time()
                    await ws.send_json(query)
                    
                    # Wait for response
                    async for msg in ws:
                        if msg.type == aiohttp.WSMsgType.TEXT:
                            data = json.loads(msg.data)
                            msg_type = data.get("type")
                            
                            if msg_type == "turn_complete":
                                elapsed = time.time() - req_start
                                results.append({
                                    "index": request_count,
                                    "success": True,
                                    "time": elapsed,
                                    "timestamp": time.time()
                                })
                                logger.info(f"✅ Request {request_count} completed in {elapsed:.2f}s")
                                break
                            elif msg_type == "error":
                                error_msg = data.get("message", "Unknown error")
                                if "rate" in error_msg.lower() or "429" in error_msg:
                                    results.append({
                                        "index": request_count,
                                        "success": False,
                                        "error": "rate_limited",
                                        "timestamp": time.time()
                                    })
                                    logger.warning(f"🚫 Request {request_count} was rate limited - waiting...")
                                    await asyncio.sleep(7)  # Wait longer after rate limit
                                break
                                
            except Exception as e:
                logger.error(f"❌ Request {request_count} failed: {e}")
                
            request_count += 1
            
            # Small delay between requests to avoid overwhelming
            await asyncio.sleep(1)
            
    finally:
        await session.close()
    
    # Final analysis
    total_time = time.time() - start_time
    successful = [r for r in results if r.get('success', False)]
    rate_limited = [r for r in results if not r.get('success', False) and r.get('error') == 'rate_limited']
    
    logger.info(f"\n📊 SUSTAINED LOAD TEST RESULTS")
    logger.info("=" * 50)
    logger.info(f"Test duration: {total_time:.1f} seconds")
    logger.info(f"Total requests attempted: {request_count}")
    logger.info(f"Successful requests: {len(successful)}")
    logger.info(f"Rate limited requests: {len(rate_limited)}")
    logger.info(f"Average RPM: {(len(successful) / total_time) * 60:.1f}")
    
    if (len(successful) / total_time) * 60 > 10:
        logger.error("❌ RATE LIMITING FAILED! Exceeded 10 RPM over sustained period")
    else:
        logger.info("✅ Rate limiting working correctly over sustained period")

async def main():
    """Run all tests."""
    logger.info("🚀 Starting real rate limiting tests...")
    logger.info("Make sure the server is running on http://localhost:8080")
    
    try:
        # Test 1: Single request
        await test_single_request()
        await asyncio.sleep(2)
        
        # Test 2: Rapid requests
        await test_rapid_requests(15)
        await asyncio.sleep(5)
        
        # Test 3: Sustained load
        await test_sustained_load(70)
        
        logger.info("\n✅ All tests completed!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
