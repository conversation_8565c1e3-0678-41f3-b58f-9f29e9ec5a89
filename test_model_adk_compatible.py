"""
Test RateLimitedLiteLlm with ADK-compatible interface
"""
import asyncio
import logging
from google.genai.types import Part, Content
from multi_tool_agent.models.rate_limited_llm import RateLimitedLiteLlm

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_model_with_adk_interface():
    """Test the model with ADK-style parameters"""
    logger.info("🧪 Testing RateLimitedLiteLlm with ADK interface...")
    
    try:
        # Create the model
        model = RateLimitedLiteLlm(
            model="novita/moonshotai/kimi-k2-instruct",
            task_type="test",
            is_critical=True
        )
        logger.info("✅ Model created successfully")
        
        # Create ADK-style content
        content = Content(
            parts=[Part(text="Hello, this is a test message")],
            role="user"
        )
        
        # Mock context (ADK typically provides this)
        class MockContext:
            def __init__(self):
                self.session_id = "test_session"
                self.user_id = "test_user"
        
        ctx = MockContext()
        
        logger.info("📤 Calling generate_content_async...")
        
        # Test with explicit stream parameter
        response_count = 0
        async for response in model.generate_content_async(content, ctx, stream=True):
            response_count += 1
            logger.info(f"📥 Response #{response_count}: {type(response)}")
            if hasattr(response, 'content'):
                logger.info(f"   Content: {response.content}")
            if hasattr(response, 'text'):
                logger.info(f"   Text: {response.text[:100] if response.text else 'None'}")
            
            # Only process first few responses to avoid long output
            if response_count >= 3:
                logger.info("   ... (stopping after 3 responses)")
                break
        
        logger.info(f"✅ Successfully received {response_count} responses")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error: {type(e).__name__}: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the test"""
    success = await test_model_with_adk_interface()
    if success:
        logger.info("\n✅ Model test passed!")
    else:
        logger.info("\n❌ Model test failed!")

if __name__ == "__main__":
    asyncio.run(main())
