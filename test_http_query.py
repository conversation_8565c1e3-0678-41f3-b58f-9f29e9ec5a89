"""
Simple HTTP test for the ADK server query endpoint
"""
import requests
import json
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_query_endpoint():
    """Test the /query endpoint directly with HTTP POST"""
    url = "http://localhost:8080/query"
    
    # Simple test query
    payload = {
        "query": "What activities are available for kids?",
        "user_id": "test_user"
    }
    
    logger.info("🚀 Testing /query endpoint with HTTP POST...")
    logger.info(f"📤 Sending query: {payload['query']}")
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        logger.info(f"📥 Response status: {response.status_code}")
        logger.info(f"📥 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                logger.info(f"✅ Response data: {json.dumps(data, indent=2)}")
            except:
                logger.info(f"📄 Response text: {response.text}")
        else:
            logger.error(f"❌ Error response: {response.text}")
            
    except requests.exceptions.Timeout:
        logger.error("❌ Request timed out after 30 seconds")
    except requests.exceptions.ConnectionError:
        logger.error("❌ Could not connect to server - make sure it's running on port 8080")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {type(e).__name__}: {str(e)}")

if __name__ == "__main__":
    test_query_endpoint()
