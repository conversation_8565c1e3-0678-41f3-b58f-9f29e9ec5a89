#!/usr/bin/env python3
"""
Test script to verify DeepInfra configuration and fallback logic.
This tests the complete migration from Targon to DeepInfra.
"""

import asyncio
import os
import sys
import logging
from unittest.mock import patch, MagicMock

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from multi_tool_agent.config import AgentConfig
from multi_tool_agent.utils.rate_limiter import HybridModelManager, get_hybrid_manager, reset_hybrid_manager

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_config_migration():
    """Test that all config values have been migrated from Targon to DeepInfra."""
    logger.info("🔧 Testing config migration from Targon to DeepInfra...")
    
    # Check that DeepInfra config values exist
    assert hasattr(AgentConfig, 'DEEPINFRA_API_KEY'), "DEEPINFRA_API_KEY not found in config"
    assert hasattr(AgentConfig, 'DEEPINFRA_API_BASE'), "DEEPINFRA_API_BASE not found in config"
    assert hasattr(AgentConfig, 'DEEPINFRA_KIMI_MODEL'), "DEEPINFRA_KIMI_MODEL not found in config"
    assert hasattr(AgentConfig, 'USE_DEEPINFRA_FALLBACK'), "USE_DEEPINFRA_FALLBACK not found in config"
    assert hasattr(AgentConfig, 'DEEPINFRA_RATE_LIMIT_RPM'), "DEEPINFRA_RATE_LIMIT_RPM not found in config"
    
    # Check expected values
    assert AgentConfig.DEEPINFRA_API_BASE == "https://api.deepinfra.com/v1/openai", f"Expected DeepInfra API base, got {AgentConfig.DEEPINFRA_API_BASE}"
    assert AgentConfig.DEEPINFRA_KIMI_MODEL == "openai/moonshotai/Kimi-K2-Instruct", f"Expected DeepInfra Kimi model, got {AgentConfig.DEEPINFRA_KIMI_MODEL}"
    assert AgentConfig.DEEPINFRA_RATE_LIMIT_RPM == 60, f"Expected 60 RPM for DeepInfra, got {AgentConfig.DEEPINFRA_RATE_LIMIT_RPM}"
    
    # Check that old Targon config values are gone or deprecated
    old_targon_attrs = ['TARGON_API_KEY', 'TARGON_API_BASE', 'TARGON_KIMI_MODEL', 'USE_TARGON_FALLBACK']
    for attr in old_targon_attrs:
        if hasattr(AgentConfig, attr):
            logger.warning(f"⚠️  Old Targon config {attr} still exists - should be cleaned up")
    
    logger.info("✅ Config migration test passed!")

def test_hybrid_manager_initialization():
    """Test that HybridModelManager initializes with DeepInfra configuration."""
    logger.info("🔧 Testing HybridModelManager initialization...")
    
    # Reset any existing manager
    reset_hybrid_manager()
    
    # Mock the DeepInfra API key to avoid actual API calls
    with patch.object(AgentConfig, 'DEEPINFRA_API_KEY', 'test_key'):
        manager = HybridModelManager()
        
        # Check that DeepInfra attributes are set correctly
        assert hasattr(manager, 'deepinfra_kimi_model'), "deepinfra_kimi_model not set"
        assert hasattr(manager, 'deepinfra_api_base'), "deepinfra_api_base not set"
        assert hasattr(manager, 'deepinfra_api_key'), "deepinfra_api_key not set"
        assert hasattr(manager, 'use_deepinfra'), "use_deepinfra not set"
        assert hasattr(manager, 'deepinfra_limiter'), "deepinfra_limiter not set"
        
        # Check that DeepInfra limiter is properly configured
        assert manager.deepinfra_limiter.provider_name == "DeepInfra"
        assert manager.deepinfra_limiter.config.rpm == 60  # DeepInfra 60 RPM
        
        # Check that old Targon attributes are gone
        old_targon_attrs = ['targon_kimi_model', 'targon_api_base', 'targon_api_key', 'use_targon', 'targon_limiter']
        for attr in old_targon_attrs:
            if hasattr(manager, attr):
                logger.error(f"❌ Old Targon attribute {attr} still exists in manager")
                assert False, f"Old Targon attribute {attr} should have been removed"
        
        logger.info("✅ HybridModelManager initialization test passed!")

async def test_fallback_logic():
    """Test that fallback logic uses DeepInfra instead of Targon."""
    logger.info("🔧 Testing fallback logic...")
    
    # Reset any existing manager
    reset_hybrid_manager()
    
    # Mock configuration for testing
    with patch.object(AgentConfig, 'DEEPINFRA_API_KEY', 'test_deepinfra_key'), \
         patch.object(AgentConfig, 'USE_DEEPINFRA_FALLBACK', True):
        
        manager = HybridModelManager()
        
        # Mock LLM call that simulates rate limiting
        async def mock_llm_call(model, **kwargs):
            if model == manager.kimi_model:
                # Simulate Novita rate limiting with proper error message
                raise Exception("429 Rate limit exceeded")
            elif model == manager.deepinfra_kimi_model:
                # Simulate successful DeepInfra call
                return f"DeepInfra response with model {model}"
            elif model == AgentConfig.FALLBACK_MODEL:
                # Gemini fallback
                return f"Gemini response with model {model}"
            else:
                return "Unexpected model fallback"
        
        # Mock the rate limiters to avoid actual timing
        from unittest.mock import AsyncMock
        with patch.object(manager.kimi_limiter, 'acquire') as mock_novita_acquire, \
             patch.object(manager.deepinfra_limiter, 'acquire') as mock_deepinfra_acquire:
            
            # Configure the mocks as proper async context managers
            mock_novita_acquire.return_value.__aenter__ = AsyncMock()
            mock_novita_acquire.return_value.__aexit__ = AsyncMock()
            mock_deepinfra_acquire.return_value.__aenter__ = AsyncMock()
            mock_deepinfra_acquire.return_value.__aexit__ = AsyncMock()
            
            # Test fallback scenario
            result = await manager.execute_with_rate_limiting(
                mock_llm_call,
                task_type="reasoning",
                is_critical=True
            )
            
            # Should fall back to DeepInfra, not Targon
            assert "DeepInfra response" in result, f"Expected DeepInfra fallback, got: {result}"
            assert "deepinfra_kimi_model" in result or "Kimi-K2-Instruct" in result, f"Expected DeepInfra model in response: {result}"
            
            logger.info("✅ Fallback logic test passed!")

def test_environment_variables():
    """Test environment variable handling for DeepInfra."""
    logger.info("🔧 Testing environment variable handling...")
    
    # Test that we can set DeepInfra environment variables
    test_env_vars = {
        'DEEPINFRA_API_KEY': 'test_deepinfra_key_123',
        'DEEPINFRA_API_BASE': 'https://api.deepinfra.com/v1/openai'
    }
    
    for key, value in test_env_vars.items():
        os.environ[key] = value
        
    # Reload config to pick up environment variables
    import importlib
    import multi_tool_agent.config as config_module
    importlib.reload(config_module)
    
    # Check that environment variables are picked up
    from multi_tool_agent.config import AgentConfig
    
    # Note: The actual implementation depends on how your config loads env vars
    # This is a basic test structure - adjust based on your actual config loading
    
    logger.info("✅ Environment variable test passed!")

async def test_rate_limiting_integration():
    """Test that rate limiting works with DeepInfra configuration."""
    logger.info("🔧 Testing rate limiting integration...")
    
    # Reset any existing manager
    reset_hybrid_manager()
    
    with patch.object(AgentConfig, 'DEEPINFRA_API_KEY', 'test_key'):
        manager = HybridModelManager()
        
        # Test that DeepInfra rate limiter is properly configured
        deepinfra_limiter = manager.deepinfra_limiter
        
        # Check rate limit configuration
        assert deepinfra_limiter.config.rpm == 60, f"Expected 60 RPM, got {deepinfra_limiter.config.rpm}"
        assert deepinfra_limiter.config.min_interval == 1.1, f"Expected 1.1s interval, got {deepinfra_limiter.config.min_interval}"
        assert deepinfra_limiter.provider_name == "DeepInfra"
        
        # Test that rate limiting doesn't crash
        is_limited = await deepinfra_limiter.is_rate_limited()
        assert isinstance(is_limited, bool), "Rate limiting check should return boolean"
        
        logger.info("✅ Rate limiting integration test passed!")

async def main():
    """Run all tests."""
    logger.info("🚀 Starting DeepInfra setup verification tests...")
    
    try:
        # Run synchronous tests
        test_config_migration()
        test_hybrid_manager_initialization()
        test_environment_variables()
        
        # Run asynchronous tests
        await test_fallback_logic()
        await test_rate_limiting_integration()
        
        logger.info("🎉 All tests passed! DeepInfra setup is working correctly.")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
