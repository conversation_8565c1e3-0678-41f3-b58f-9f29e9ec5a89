"""
Simple WebSocket test with detailed logging
"""
import asyncio
import websockets
import json
import logging
import time

logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_simple_query():
    """Test a single query with detailed logging"""
    uri = "ws://localhost:8080/ws"
    
    logger.info(f"🚀 Connecting to {uri}...")
    
    try:
        async with websockets.connect(uri) as websocket:
            logger.info("✅ WebSocket connected successfully")
            
            # Prepare test message
            message = {
                "content": "What swimming classes are available for kids?",
                "session_id": f"test_{int(time.time())}",
                "user_id": "test_user",
                "type": "chat_message"
            }
            
            # Send message
            logger.info(f"📤 Sending message: {json.dumps(message, indent=2)}")
            await websocket.send(json.dumps(message))
            logger.info("✅ Message sent successfully")
            
            # Read responses with timeout
            start_time = time.time()
            timeout = 30  # 30 second timeout
            response_count = 0
            
            while True:
                try:
                    # Use wait_for to add timeout to receive
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_count += 1
                    
                    try:
                        data = json.loads(response)
                        logger.info(f"📥 Response #{response_count}: {json.dumps(data, indent=2)}")
                        
                        # Check response type
                        if data.get("type") == "ack":
                            logger.info("✅ Received acknowledgment")
                        elif data.get("type") == "text_chunk":
                            logger.info(f"📝 Text chunk: {data.get('content', '')[:100]}...")
                        elif data.get("type") == "turn_complete":
                            logger.info("✅ Turn complete - Agent finished processing")
                            break
                        elif data.get("type") == "error":
                            logger.error(f"❌ Error from server: {data.get('message', 'Unknown error')}")
                            break
                        elif data.get("type") == "ping":
                            logger.debug("🏓 Received ping")
                            
                    except json.JSONDecodeError:
                        logger.warning(f"⚠️ Non-JSON response: {response[:100]}")
                        
                except asyncio.TimeoutError:
                    elapsed = time.time() - start_time
                    if elapsed > timeout:
                        logger.warning(f"⏱️ Timeout after {elapsed:.1f}s - no more responses")
                        break
                    else:
                        logger.debug(f"⏱️ No response in 5s, waiting... (total: {elapsed:.1f}s)")
                        continue
                        
                except websockets.exceptions.ConnectionClosed:
                    logger.error("❌ WebSocket connection closed by server")
                    break
                    
            logger.info(f"📊 Total responses received: {response_count}")
            
    except websockets.exceptions.WebSocketException as e:
        logger.error(f"❌ WebSocket error: {type(e).__name__}: {str(e)}")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {type(e).__name__}: {str(e)}")

async def main():
    """Run the test"""
    logger.info("🧪 Starting simple WebSocket test...")
    await test_simple_query()
    
    # Also check API stats
    logger.info("\n🔍 Checking API stats...")
    import requests
    try:
        response = requests.get("http://localhost:8080/api-stats")
        if response.status_code == 200:
            stats = response.json()
            api_usage = stats.get("api_usage", {})
            logger.info(f"📊 Total API calls: {api_usage.get('total_calls', 0)}")
            logger.info(f"🧠 Kimi calls: {api_usage.get('kimi_calls', 0)}")
            logger.info(f"⚡ Fallback calls: {api_usage.get('fallback_calls', 0)}")
            logger.info(f"🎯 Current Kimi RPM: {api_usage.get('current_kimi_rpm', 0)}/10")
    except Exception as e:
        logger.error(f"❌ Failed to get API stats: {e}")

if __name__ == "__main__":
    asyncio.run(main())
