#!/usr/bin/env python3
"""
Test script to validate the fixes applied to the codebase.
"""

import sys
import os
import asyncio
import logging

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all imports work correctly."""
    try:
        from multi_tool_agent.config import AgentConfig
        logger.info("✅ AgentConfig imported successfully")
        
        # Test config values
        logger.info(f"Kimi model: {AgentConfig.KIMI_MODEL}")
        logger.info(f"Targon model: {AgentConfig.TARGON_KIMI_MODEL}")
        logger.info(f"Fallback model: {AgentConfig.FALLBACK_MODEL}")
        
        return True
    except Exception as e:
        logger.error(f"❌ Import error: {e}")
        return False

def test_rate_limiter():
    """Test that rate limiter components work."""
    try:
        from multi_tool_agent.utils.rate_limiter import get_hybrid_manager, RateLimitConfig
        logger.info("✅ Rate limiter imported successfully")
        
        # Test configuration
        config = RateLimitConfig(
            rpm=10,
            min_interval=6.5,
            max_queue_size=50,
            queue_timeout=30.0,
            enable_fallback=True
        )
        logger.info(f"Rate limit config: {config.rpm} RPM, {config.min_interval}s interval")
        
        return True
    except Exception as e:
        logger.error(f"❌ Rate limiter error: {e}")
        return False

def test_activity_search_tools():
    """Test that activity search tools work."""
    try:
        from multi_tool_agent.tools.activity_search_tools import _create_qdrant_filter
        logger.info("✅ Activity search tools imported successfully")
        
        # Test filter creation
        test_filters = {
            "location": "burnaby",
            "max_price": 100.0,
            "age": 5,
            "is_open": True
        }
        
        filter_obj = _create_qdrant_filter(test_filters)
        logger.info(f"Created filter with {len(filter_obj.must) if filter_obj else 0} conditions")
        
        return True
    except Exception as e:
        logger.error(f"❌ Activity search tools error: {e}")
        return False

def test_rate_limited_model():
    """Test that rate limited model works."""
    try:
        from multi_tool_agent.models.rate_limited_llm import create_ultra_optimized_model
        logger.info("✅ Rate limited model imported successfully")
        
        # This would fail without proper environment setup, but at least we can test creation
        logger.info("Rate limited model components are available")
        
        return True
    except Exception as e:
        logger.error(f"❌ Rate limited model error: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("🚀 Starting validation tests...")
    
    tests = [
        ("Config Import", test_imports),
        ("Rate Limiter", test_rate_limiter),
        ("Activity Search Tools", test_activity_search_tools),
        ("Rate Limited Model", test_rate_limited_model),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n=== Running {test_name} Test ===")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n=== Test Summary ===")
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        logger.info("🎉 All tests passed! The fixes are working correctly.")
        return True
    else:
        logger.error("⚠️ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
