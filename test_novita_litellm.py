#!/usr/bin/env python3
"""
Test script to verify correct LiteLLM model naming for Novi<PERSON>
"""

import os
import asyncio
import litellm
from litellm import acompletion

# Set up Novita API key
os.environ["NOVITA_API_KEY"] = os.getenv("NOVITA_API_KEY", "")

async def test_novita_models():
    """Test various model name formats for Novita"""
    
    # Test different model name formats
    model_names = [
        "novita/moonshotai/kimi-k2-instruct",
        "novita/deepseek/deepseek-r1-turbo",  # From the docs example
        "moonshotai/kimi-k2-instruct",
        "openai/moonshotai/kimi-k2-instruct",
    ]
    
    for model_name in model_names:
        print(f"\n🧪 Testing model: {model_name}")
        try:
            response = await acompletion(
                model=model_name,
                messages=[{"role": "user", "content": "Say 'Hello' and nothing else."}],
                max_tokens=10
            )
            print(f"✅ Success! Response: {response.choices[0].message.content}")
            return model_name  # Return the working model name
        except Exception as e:
            print(f"❌ Failed: {type(e).__name__}: {str(e)}")
    
    return None

async def test_with_base_url():
    """Test with explicit base URL"""
    print("\n🧪 Testing with explicit base URL...")
    
    try:
        # Set base URL for OpenAI compatibility
        os.environ["OPENAI_API_BASE"] = "https://api.novita.ai/v3/openai"
        os.environ["OPENAI_API_KEY"] = os.getenv("NOVITA_API_KEY", "")
        
        response = await acompletion(
            model="openai/moonshotai/kimi-k2-instruct",
            messages=[{"role": "user", "content": "Say 'Hello' and nothing else."}],
            max_tokens=10
        )
        print(f"✅ Success with OpenAI format! Response: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"❌ Failed: {type(e).__name__}: {str(e)}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Testing LiteLLM with Novita API...")
    
    # First check if API key is set
    if not os.getenv("NOVITA_API_KEY"):
        print("⚠️  Warning: NOVITA_API_KEY not set in environment")
        print("   You can set it with: set NOVITA_API_KEY=your-key-here")
        return
    
    # Test 1: Try different model name formats
    working_model = await test_novita_models()
    
    if working_model:
        print(f"\n✅ Working model name format: {working_model}")
    else:
        # Test 2: Try with explicit base URL
        if await test_with_base_url():
            print("\n✅ OpenAI compatibility mode works!")
        else:
            print("\n❌ Could not find a working configuration")
    
    # Show LiteLLM version
    print(f"\n📦 LiteLLM version: {litellm.__version__}")

if __name__ == "__main__":
    asyncio.run(main())
