# Community Activity Finder Agent

This project is a sophisticated AI system designed to help users find and explore community activities. It understands complex natural language queries, including back-to-back scheduling, and provides accurate results from a real-time database using hybrid vector search.

## 🚀 Key Features

- **Natural Language Understanding**: Uses an intelligent orchestrator to parse complex user queries into structured searches.
- **Hybrid Search Capabilities**:
    - **Back-to-Back Search**: Finds consecutive activities with minimal time gaps at the same facility.
    - **Semantic + Keyword Search**: Combines dense vectors for semantic understanding with sparse vectors for keyword matching.
    - **Advanced Filtering**: Supports age, location, day of week, price, and date filtering.
- **High-Performance Architecture**: Built on Google's Agent Development Kit (ADK) using a direct-function-calling orchestrator for speed and accuracy.
- **Automated Cloud Deployment**: A single, robust script handles dependencies, builds, and deploys the entire agent and UI to Google Cloud Run.

---

## 🏗️ Current System Architecture

The system has been simplified to a streamlined, high-performance architecture:

### Core Components

```mermaid
graph TD
    A[User Query] --> B[Orchestrator Agent]
    B --> C[fast_activity_search Tool]
    C --> D[Hybrid Qdrant Search]
    D --> E[Dense + Sparse Vectors]
    E --> F[Structured Results]
    F --> G[Formatted Response]
```

### 1. Orchestrator Agent (`multi_tool_agent/orchestrator.py`)
- **Purpose**: Main agent that understands user intent and extracts structured filters
- **Capabilities**: 
  - Parses natural language into search queries and filters
  - Handles back-to-back class requests
  - Routes to appropriate tools
- **Model**: Gemini 1.5 Flash for fast, accurate parsing

### 2. Fast Activity Search Tool (`multi_tool_agent/tools/activity_search_tools.py`)
- **Purpose**: Performs hybrid search with direct result formatting
- **Capabilities**:
  - Combines dense (semantic) and sparse (keyword) vector search
  - Applies structured filters at database level
  - Finds back-to-back class pairs
  - Returns formatted markdown directly (skips expensive LLM calls)
- **Performance**: Sub-second response times

### 3. Hybrid Qdrant Database
- **Dense Vectors**: BAAI/bge-small-en-v1.5 for semantic understanding
- **Sparse Vectors**: Qdrant/bm25 for keyword matching
- **ColBERT Vectors**: sentence-transformers/ColBERT-small for fine-grained matching (optional)
- **Payload Indexes**: Lightning-fast filtering on all fields
- **Data**: ~10,000+ activities from Burnaby and New Westminster
- **Vector Configuration**: Uses named vectors ("dense", "sparse", "colbert") for multi-vector search

### 4. Data Pipeline
- **Source**: Automated scraping from city recreation websites
- **Storage**: `community_activities.jsonl` (current data)
- **Ingestion**: `update_qdrant_activities.py` (hybrid vector generation)
- **Setup**: `setup_qdrant_collection.py` (collection configuration)

---

## 📁 Current File Structure

```
agent_google/
├── multi_tool_agent/
│   ├── orchestrator.py          # Main orchestrator agent
│   ├── run_agent.py             # Agent runner
│   ├── config.py                # System configuration
│   ├── update_qdrant_activities.py  # Data ingestion
│   ├── tools/
│   │   ├── activity_search_tools.py  # Core hybrid search
│   │   └── __init__.py
│   ├── ingestion/
│   │   └── setup_qdrant_collection.py  # Qdrant setup
│   └── subagents/
│       └── __init__.py          # Placeholder agents
├── community_activities.jsonl   # Current activity data
├── test_back_to_back.py         # Test script
├── requirements.txt             # Dependencies
└── archive/                     # Archived development files
```

---

## ⚙️ How to Run

### Step 1: Set Up Your Environment
1. **Clone the repository.**
2. **Create and activate a Python virtual environment.**
    ```bash
    python -m venv .venv
    source .venv/bin/activate  # On Windows, use `.venv\Scripts\activate`
    ```
3. **Install the required dependencies.**
    ```bash
    pip install -r requirements.txt
    ```
4. **Set up your environment variables.** Create a `.env` file in the project root:
    ```
    GOOGLE_API_KEY="your_google_api_key"
    QDRANT_URL="your_qdrant_cloud_url"
    QDRANT_API_KEY="your_qdrant_api_key"
    ```

### Step 2: Set Up Qdrant Collection
```bash
python multi_tool_agent/ingestion/setup_qdrant_collection.py
```

### Step 3: Ingest Activity Data
```bash
python multi_tool_agent/update_qdrant_activities.py
```

### Step 4: Test the System
```bash
python test_back_to_back.py
```

### Step 5: Run the Agent
```bash
python multi_tool_agent/run_agent.py
```

---

## 🔧 Recent Improvements

### Hybrid Search Implementation (Latest)
- **Dense + Sparse Vectors**: Combines semantic understanding with keyword matching
- **Named Vector Search**: Explicitly specifies vector type ("dense") in query_points calls
- **Server-Side Embeddings**: Uses Qdrant's server-side embedding generation for faster performance
- **Fusion Queries**: Uses Qdrant's fusion algorithm for optimal result ranking
- **Database-Level Filtering**: All filters applied at Qdrant level for maximum performance
- **Skip Summarization**: Direct result formatting eliminates expensive LLM calls

### Back-to-Back Class Search
- **Temporal Pairing**: Finds consecutive classes at the same facility
- **Smart Gap Detection**: Configurable time gap limits (default 30 minutes)
- **Common Day Matching**: Ensures classes share at least one day of the week
- **Structured Output**: Clear presentation of paired activities

### System Simplification
- **Archived Legacy Code**: Moved unused files to `archive/` directory
- **Streamlined Architecture**: Single orchestrator + hybrid search tool
- **Placeholder Subagents**: Maintains compatibility while removing complexity
- **Clean Dependencies**: Only essential imports and tools

---

## 📊 Performance Metrics

- **Search Speed**: Sub-second response times
- **Data Coverage**: 10,000+ activities from multiple cities
- **Search Accuracy**: Hybrid approach combines semantic and keyword matching
- **Filter Performance**: Database-level filtering with payload indexes
- **Memory Usage**: Optimized vector storage and retrieval

---

## 🗄️ Archive

The `archive/` directory contains files from the development process that are no longer needed:
- Development documentation and plans
- Previous agent implementations
- Old data processing scripts
- Unused tools and subagents
- Scraping and pipeline scripts (completed)

See `archive/README.md` for detailed documentation of archived files.

---

## 🔍 Troubleshooting

### Common Issues

1. **Qdrant 400 Bad Request Error**
   - **Cause**: Not specifying which vector to use in multi-vector collections
   - **Solution**: Use `models.NamedVector` with `name="dense"` parameter in query_points calls
   - **Example**:
     ```python
     query=models.NamedVector(
         name="dense",  # Specify which vector to use
         vector=models.Document(
             text=query,
             model=AgentConfig.EMBEDDING_MODEL
         )
     )
     ```

2. **Search Fallback to Simple Mode**
   - **Cause**: Advanced search failing due to missing vector specification
   - **Impact**: Degrades to simple scroll-based search without semantic understanding
   - **Solution**: Ensure proper vector configuration in query_points calls

3. **Collection Setup Issues**
   - **Cause**: Mismatched vector names between collection setup and query code
   - **Solution**: Verify vector names match in both `setup_qdrant_collection.py` and search tools

---

## 🚀 Deployment

The system can be deployed to Google Cloud Run using the provided deployment scripts:
- `deploy_cloud_run_adk.sh` - Automated deployment script
- `Dockerfile` - Container configuration
- `deployment.yaml` - Kubernetes deployment (if needed)

The deployment process automatically handles environment variables, dependency installation, and service configuration.