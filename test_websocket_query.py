#!/usr/bin/env python3
"""
Test WebSocket query to verify rate limiting is working with the agent.
"""

import asyncio
import time
import logging
import aiohttp
import json

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Server configuration
WS_URL = "ws://localhost:8080/ws"

async def test_single_query():
    """Test a single query to the WebSocket server."""
    logger.info("🧪 Testing single query to WebSocket server...")
    
    session = aiohttp.ClientSession()
    try:
        async with session.ws_connect(WS_URL) as ws:
            # Send a test query
            query = {
                "type": "chat_message",
                "content": "Find swimming classes for a 5 year old in New Westminster",
                "session_id": f"test_{int(time.time())}",
                "user_id": "test_user"
            }
            
            await ws.send_json(query)
            logger.info("📤 Sent query to server")
            
            # Track API calls and responses
            start_time = time.time()
            response_chunks = []
            api_calls_detected = []
            
            async for msg in ws:
                if msg.type == aiohttp.WSMsgType.TEXT:
                    data = json.loads(msg.data)
                    msg_type = data.get("type")
                    
                    if msg_type == "ack":
                        logger.info("✅ Received acknowledgment")
                        
                    elif msg_type == "text_chunk":
                        content = data.get("content", "")
                        response_chunks.append(content)
                        
                        # Look for model usage indicators in logs
                        if "Using Kimi K2" in content:
                            api_calls_detected.append("Kimi")
                        elif "Using fallback model" in content:
                            api_calls_detected.append("Gemini")
                            
                    elif msg_type == "turn_complete":
                        elapsed = time.time() - start_time
                        logger.info(f"✅ Response complete in {elapsed:.2f}s")
                        
                        # Show response summary
                        full_response = "".join(response_chunks)
                        logger.info(f"📝 Response length: {len(full_response)} characters")
                        
                        if api_calls_detected:
                            logger.info(f"🔍 Detected API calls: {api_calls_detected}")
                        
                        # Show first 200 chars of response
                        preview = full_response[:200] + "..." if len(full_response) > 200 else full_response
                        logger.info(f"📄 Response preview: {preview}")
                        break
                        
                    elif msg_type == "error":
                        error_msg = data.get("message", "Unknown error")
                        logger.error(f"❌ Error: {error_msg}")
                        
                        # Check if it's a rate limit error
                        if "rate" in error_msg.lower() or "429" in error_msg:
                            logger.warning("🚫 Rate limit detected!")
                        break
                        
                elif msg.type == aiohttp.WSMsgType.ERROR:
                    logger.error(f"❌ WebSocket error: {ws.exception()}")
                    break
                    
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise
    finally:
        await session.close()

async def test_multiple_queries(num_queries=5):
    """Test multiple sequential queries to check rate limiting."""
    logger.info(f"\n🧪 Testing {num_queries} sequential queries...")
    
    queries = [
        "Find swimming classes for a 5 year old",
        "What back-to-back activities are available?",
        "Show me gymnastics classes in Burnaby",
        "Find art classes for kids on weekends",
        "What activities are available for toddlers?"
    ]
    
    results = []
    
    for i in range(min(num_queries, len(queries))):
        logger.info(f"\n📌 Query {i+1}: {queries[i]}")
        
        session = aiohttp.ClientSession()
        try:
            async with session.ws_connect(WS_URL) as ws:
                query = {
                    "type": "chat_message",
                    "content": queries[i],
                    "session_id": f"test_{i}_{int(time.time())}",
                    "user_id": f"test_user_{i}"
                }
                
                await ws.send_json(query)
                
                start_time = time.time()
                rate_limited = False
                
                async for msg in ws:
                    if msg.type == aiohttp.WSMsgType.TEXT:
                        data = json.loads(msg.data)
                        msg_type = data.get("type")
                        
                        if msg_type == "turn_complete":
                            elapsed = time.time() - start_time
                            results.append({
                                "query": i,
                                "success": True,
                                "time": elapsed,
                                "rate_limited": rate_limited
                            })
                            logger.info(f"✅ Query {i+1} completed in {elapsed:.2f}s")
                            break
                            
                        elif msg_type == "error":
                            error_msg = data.get("message", "")
                            if "rate" in error_msg.lower() or "429" in error_msg:
                                rate_limited = True
                                logger.warning(f"🚫 Query {i+1} was rate limited")
                            
                            results.append({
                                "query": i,
                                "success": False,
                                "error": error_msg,
                                "rate_limited": rate_limited
                            })
                            break
                            
        except Exception as e:
            logger.error(f"❌ Query {i+1} failed: {e}")
            results.append({
                "query": i,
                "success": False,
                "error": str(e)
            })
        finally:
            await session.close()
            
        # Small delay between queries
        await asyncio.sleep(1)
    
    # Summary
    logger.info("\n📊 QUERY TEST SUMMARY")
    logger.info("=" * 50)
    successful = [r for r in results if r.get("success")]
    rate_limited = [r for r in results if r.get("rate_limited")]
    
    logger.info(f"Total queries: {len(results)}")
    logger.info(f"Successful: {len(successful)}")
    logger.info(f"Rate limited: {len(rate_limited)}")
    
    if rate_limited:
        logger.warning("⚠️ Some queries were rate limited")
    else:
        logger.info("✅ No rate limiting detected")

async def check_api_stats():
    """Check the API stats endpoint."""
    logger.info("\n🔍 Checking API stats...")
    
    session = aiohttp.ClientSession()
    try:
        async with session.get("http://localhost:8080/api-stats") as resp:
            if resp.status == 200:
                stats = await resp.json()
                
                api_usage = stats.get("api_usage", {})
                logger.info(f"📊 Total API calls: {api_usage.get('total_calls', 0)}")
                logger.info(f"🧠 Kimi calls: {api_usage.get('kimi_calls', 0)}")
                logger.info(f"⚡ Fallback calls: {api_usage.get('fallback_calls', 0)}")
                logger.info(f"🚫 Rate limited calls: {api_usage.get('rate_limited_calls', 0)}")
                logger.info(f"🎯 Current Kimi RPM: {api_usage.get('current_kimi_rpm', 0)}/10")
                
                suggestions = api_usage.get('optimization_suggestions', [])
                if suggestions:
                    logger.info("\n💡 Optimization suggestions:")
                    for suggestion in suggestions:
                        logger.info(f"  {suggestion}")
            else:
                logger.error(f"❌ Failed to get API stats: {resp.status}")
                
    except Exception as e:
        logger.error(f"❌ Error checking API stats: {e}")
    finally:
        await session.close()

async def main():
    """Run all tests."""
    logger.info("🚀 Starting WebSocket query tests...")
    logger.info("Make sure the server is running on http://localhost:8080")
    
    try:
        # Test 1: Single query
        await test_single_query()
        await asyncio.sleep(2)
        
        # Test 2: Multiple queries
        await test_multiple_queries(5)
        await asyncio.sleep(2)
        
        # Check API stats
        await check_api_stats()
        
        logger.info("\n✅ All tests completed!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
